{"name": "flavio/smart-auth", "description": "Complete authentication and user registration using Firebase Authentication with optional Twilio OTP integration", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "authors": [{"name": "Flavio", "email": "<EMAIL>", "homepage": "https://moflavio.xyz"}], "require": {"php": ">=8.0", "giggsey/libphonenumber-for-php": "^8.13", "firebase/php-jwt": "^6.8", "guzzlehttp/guzzle": "^7.8", "twilio/sdk": "^7.0"}, "require-dev": {"phpunit/phpunit": "^10.0", "wp-coding-standards/wpcs": "^3.0", "dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "autoload": {"psr-4": {"SmartAuth\\": "includes/"}}, "autoload-dev": {"psr-4": {"SmartAuth\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs": "phpcs --standard=WordPress --extensions=php --ignore=vendor/ .", "cbf": "phpcbf --standard=WordPress --extensions=php --ignore=vendor/ ."}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}