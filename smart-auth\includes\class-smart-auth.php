<?php
/**
 * Main Smart Auth Plugin Class
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main Smart Auth class
 */
class Smart_Auth {
    
    /**
     * Plugin instance
     *
     * @var Smart_Auth
     */
    private static $instance = null;
    
    /**
     * Firebase Auth handler
     *
     * @var Smart_Auth_Firebase
     */
    public $firebase_auth;
    
    /**
     * Twilio Auth handler
     *
     * @var Smart_Auth_Twilio
     */
    public $twilio_auth;
    
    /**
     * JWT handler
     *
     * @var Smart_Auth_JWT_Handler
     */
    public $jwt_handler;
    
    /**
     * User sync handler
     *
     * @var Smart_Auth_User_Sync
     */
    public $user_sync;
    
    /**
     * Admin settings
     *
     * @var Smart_Auth_Admin_Settings
     */
    public $admin_settings;
    
    /**
     * Get plugin instance
     *
     * @return Smart_Auth
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the plugin
     */
    private function init() {
        // Load dependencies
        $this->load_dependencies();
        
        // Initialize components
        $this->init_components();
        
        // Setup hooks
        $this->setup_hooks();
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Core classes
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-firebase-auth.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-twilio-auth.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-jwt-handler.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-user-sync.php';
        
        // Admin classes
        require_once SMART_AUTH_PLUGIN_DIR . 'admin/class-admin-settings.php';
        
        // REST API classes
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/rest-api/class-auth-endpoints.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/rest-api/class-user-endpoints.php';
        
        // Widget classes (load conditionally)
        if (did_action('elementor/loaded')) {
            require_once SMART_AUTH_PLUGIN_DIR . 'widgets/elementor/class-elementor-widgets.php';
        }
        
        if (class_exists('Bricks\Elements')) {
            require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/class-bricks-elements.php';
        }
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        $this->firebase_auth = new Smart_Auth_Firebase();
        $this->twilio_auth = new Smart_Auth_Twilio();
        $this->jwt_handler = new Smart_Auth_JWT_Handler();
        $this->user_sync = new Smart_Auth_User_Sync();
        $this->admin_settings = new Smart_Auth_Admin_Settings();
        
        // Initialize REST API endpoints
        new Smart_Auth_Auth_Endpoints();
        new Smart_Auth_User_Endpoints();
        
        // Initialize widgets if page builders are active
        if (did_action('elementor/loaded')) {
            new Smart_Auth_Elementor_Widgets();
        }
        
        if (class_exists('Bricks\Elements')) {
            new Smart_Auth_Bricks_Elements();
        }
    }
    
    /**
     * Setup WordPress hooks
     */
    private function setup_hooks() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Register shortcodes
        add_action('init', array($this, 'register_shortcodes'));
        
        // AJAX handlers
        add_action('wp_ajax_smart_auth_firebase_verify', array($this, 'ajax_firebase_verify'));
        add_action('wp_ajax_nopriv_smart_auth_firebase_verify', array($this, 'ajax_firebase_verify'));
        add_action('wp_ajax_smart_auth_send_otp', array($this, 'ajax_send_otp'));
        add_action('wp_ajax_nopriv_smart_auth_send_otp', array($this, 'ajax_send_otp'));
        add_action('wp_ajax_smart_auth_verify_otp', array($this, 'ajax_verify_otp'));
        add_action('wp_ajax_nopriv_smart_auth_verify_otp', array($this, 'ajax_verify_otp'));
        
        // Login/logout hooks
        add_action('wp_login', array($this, 'on_user_login'), 10, 2);
        add_action('wp_logout', array($this, 'on_user_logout'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        wp_enqueue_script(
            'smart-auth-frontend',
            SMART_AUTH_PLUGIN_URL . 'assets/js/smart-auth.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
        
        wp_enqueue_style(
            'smart-auth-frontend',
            SMART_AUTH_PLUGIN_URL . 'assets/css/smart-auth.css',
            array(),
            SMART_AUTH_VERSION
        );
        
        // Localize script with settings
        wp_localize_script('smart-auth-frontend', 'smartAuth', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('smart_auth_nonce'),
            'firebaseConfig' => $this->get_firebase_config(),
            'strings' => array(
                'loading' => __('Loading...', 'smart-auth'),
                'error' => __('An error occurred. Please try again.', 'smart-auth'),
                'success' => __('Success!', 'smart-auth'),
                'invalidPhone' => __('Please enter a valid phone number.', 'smart-auth'),
                'otpSent' => __('OTP sent successfully.', 'smart-auth'),
                'otpInvalid' => __('Invalid OTP code.', 'smart-auth'),
            )
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on Smart Auth admin pages
        if (strpos($hook, 'smart-auth') === false) {
            return;
        }
        
        wp_enqueue_script(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
        
        wp_enqueue_style(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            SMART_AUTH_VERSION
        );
    }
    
    /**
     * Register shortcodes
     */
    public function register_shortcodes() {
        add_shortcode('smart_auth_form', array($this, 'shortcode_auth_form'));
        add_shortcode('smart_auth_button', array($this, 'shortcode_auth_button'));
        add_shortcode('smart_auth_otp', array($this, 'shortcode_otp_form'));
        add_shortcode('smart_auth_social_login', array($this, 'shortcode_social_login'));
    }
    
    /**
     * Get Firebase configuration for frontend
     */
    private function get_firebase_config() {
        $settings = get_option('smart_auth_firebase_settings', array());
        
        return array(
            'apiKey' => isset($settings['api_key']) ? $settings['api_key'] : '',
            'authDomain' => isset($settings['auth_domain']) ? $settings['auth_domain'] : '',
            'projectId' => isset($settings['project_id']) ? $settings['project_id'] : '',
            'storageBucket' => isset($settings['storage_bucket']) ? $settings['storage_bucket'] : '',
        );
    }
    
    /**
     * Plugin activation
     */
    public static function activate() {
        // Create default options
        add_option('smart_auth_version', SMART_AUTH_VERSION);
        add_option('smart_auth_firebase_settings', array());
        add_option('smart_auth_twilio_settings', array());
        add_option('smart_auth_jwt_settings', array(
            'secret_key' => wp_generate_password(64, true, true),
            'expiration' => 24 * HOUR_IN_SECONDS,
        ));
        add_option('smart_auth_user_settings', array(
            'auto_create_users' => true,
            'default_role' => 'subscriber',
            'sync_profile_picture' => true,
        ));
        add_option('smart_auth_security_settings', array(
            'rate_limit_attempts' => 3,
            'rate_limit_window' => 15 * MINUTE_IN_SECONDS,
            'otp_resend_cooldown' => 60,
        ));
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove options
        delete_option('smart_auth_version');
        delete_option('smart_auth_firebase_settings');
        delete_option('smart_auth_twilio_settings');
        delete_option('smart_auth_jwt_settings');
        delete_option('smart_auth_user_settings');
        delete_option('smart_auth_security_settings');
        
        // Remove user meta
        delete_metadata('user', 0, 'firebase_uid', '', true);
        delete_metadata('user', 0, 'auth_provider', '', true);
        delete_metadata('user', 0, 'phone_number', '', true);
        delete_metadata('user', 0, 'profile_picture_url', '', true);
        delete_metadata('user', 0, 'last_firebase_sync', '', true);
        
        // Clean up transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_smart_auth_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_smart_auth_%'");
    }
    
    /**
     * Auth form shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Shortcode output
     */
    public function shortcode_auth_form($atts) {
        // Enqueue assets
        $this->enqueue_frontend_assets();

        // Load template
        ob_start();
        include SMART_AUTH_PLUGIN_DIR . 'templates/auth-form.php';
        return ob_get_clean();
    }

    /**
     * Auth button shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Shortcode output
     */
    public function shortcode_auth_button($atts) {
        $defaults = array(
            'provider' => 'google',
            'text' => '',
            'redirect_url' => '',
            'css_class' => '',
        );

        $atts = shortcode_atts($defaults, $atts, 'smart_auth_button');

        // Enqueue assets
        $this->enqueue_frontend_assets();

        $provider = sanitize_text_field($atts['provider']);
        $text = !empty($atts['text']) ? sanitize_text_field($atts['text']) : sprintf(__('Sign in with %s', 'smart-auth'), ucfirst($provider));
        $redirect_url = esc_url($atts['redirect_url']);
        $css_class = sanitize_html_class($atts['css_class']);

        $button_classes = array(
            'smart-auth-button',
            'smart-auth-' . $provider . '-button',
        );

        if (!empty($css_class)) {
            $button_classes[] = $css_class;
        }

        return sprintf(
            '<button type="button" class="%s" data-provider="%s" data-redirect-url="%s">%s</button>',
            esc_attr(implode(' ', $button_classes)),
            esc_attr($provider),
            esc_attr($redirect_url),
            esc_html($text)
        );
    }

    /**
     * OTP form shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Shortcode output
     */
    public function shortcode_otp_form($atts) {
        // Enqueue assets
        $this->enqueue_frontend_assets();

        // Load template
        ob_start();
        include SMART_AUTH_PLUGIN_DIR . 'templates/otp-form.php';
        return ob_get_clean();
    }

    /**
     * Social login shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Shortcode output
     */
    public function shortcode_social_login($atts) {
        $defaults = array(
            'providers' => 'google,facebook',
            'layout' => 'vertical',
            'show_icons' => 'true',
            'redirect_url' => '',
            'css_class' => '',
        );

        $atts = shortcode_atts($defaults, $atts, 'smart_auth_social_login');

        // Enqueue assets
        $this->enqueue_frontend_assets();

        $providers = array_map('trim', explode(',', $atts['providers']));
        $layout = sanitize_text_field($atts['layout']);
        $show_icons = filter_var($atts['show_icons'], FILTER_VALIDATE_BOOLEAN);
        $redirect_url = esc_url($atts['redirect_url']);
        $css_class = sanitize_html_class($atts['css_class']);

        $wrapper_classes = array(
            'smart-auth-social-login',
            'layout-' . $layout,
        );

        if (!empty($css_class)) {
            $wrapper_classes[] = $css_class;
        }

        $output = '<div class="' . esc_attr(implode(' ', $wrapper_classes)) . '" data-redirect-url="' . esc_attr($redirect_url) . '">';

        foreach ($providers as $provider) {
            $provider = sanitize_text_field($provider);
            $text = sprintf(__('Continue with %s', 'smart-auth'), ucfirst($provider));

            $output .= sprintf(
                '<button type="button" class="smart-auth-button smart-auth-%s-button" data-provider="%s">',
                esc_attr($provider),
                esc_attr($provider)
            );

            if ($show_icons) {
                $output .= '<span class="button-icon"></span>';
            }

            $output .= '<span class="button-text">' . esc_html($text) . '</span>';
            $output .= '</button>';
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * AJAX handler for Firebase verification
     */
    public function ajax_firebase_verify() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'smart-auth')));
        }

        // Get parameters
        $id_token = sanitize_text_field($_POST['id_token']);
        $provider = sanitize_text_field($_POST['provider']);

        if (empty($id_token)) {
            wp_send_json_error(array('message' => __('ID token is required.', 'smart-auth')));
        }

        // Get Firebase auth handler
        $firebase_auth = new Smart_Auth_Firebase();

        // Verify the token
        $firebase_user = $firebase_auth->verify_id_token($id_token);
        if (is_wp_error($firebase_user)) {
            wp_send_json_error(array('message' => $firebase_user->get_error_message()));
        }

        // Get user sync handler
        $user_sync = new Smart_Auth_User_Sync();

        // Sync or create WordPress user
        $wp_user = $user_sync->sync_user($firebase_user, $provider ?: 'firebase');
        if (is_wp_error($wp_user)) {
            wp_send_json_error(array('message' => $wp_user->get_error_message()));
        }

        // Log the user in
        wp_set_current_user($wp_user->ID);
        wp_set_auth_cookie($wp_user->ID);

        // Fire action
        do_action('smart_auth_user_authenticated', $wp_user, $firebase_user, 'firebase');

        wp_send_json_success(array(
            'message' => __('Authentication successful.', 'smart-auth'),
            'redirect_url' => apply_filters('smart_auth_redirect_url', home_url(), $wp_user),
        ));
    }

    /**
     * AJAX handler for sending OTP
     */
    public function ajax_send_otp() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'smart-auth')));
        }

        // Get parameters
        $phone_number = sanitize_text_field($_POST['phone_number']);
        $provider = sanitize_text_field($_POST['provider']);

        if (empty($phone_number)) {
            wp_send_json_error(array('message' => __('Phone number is required.', 'smart-auth')));
        }

        // Choose provider
        $provider = $provider ?: 'firebase';

        if ($provider === 'twilio') {
            $auth_handler = new Smart_Auth_Twilio();
        } else {
            $auth_handler = new Smart_Auth_Firebase();
        }

        // Send OTP
        $result = $auth_handler->send_otp($phone_number);
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }

        wp_send_json_success(array(
            'message' => __('OTP sent successfully.', 'smart-auth'),
            'session_info' => isset($result['session_info']) ? $result['session_info'] : null,
            'sid' => isset($result['sid']) ? $result['sid'] : null,
        ));
    }

    /**
     * AJAX handler for verifying OTP
     */
    public function ajax_verify_otp() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'smart-auth')));
        }

        // Get parameters
        $phone_number = sanitize_text_field($_POST['phone_number']);
        $otp_code = sanitize_text_field($_POST['otp_code']);
        $session_info = sanitize_text_field($_POST['session_info']);
        $provider = sanitize_text_field($_POST['provider']);

        if (empty($phone_number) || empty($otp_code)) {
            wp_send_json_error(array('message' => __('Phone number and OTP code are required.', 'smart-auth')));
        }

        // Choose provider
        $provider = $provider ?: 'firebase';

        if ($provider === 'twilio') {
            $auth_handler = new Smart_Auth_Twilio();
            $result = $auth_handler->verify_otp($phone_number, $otp_code);
        } else {
            $auth_handler = new Smart_Auth_Firebase();
            $result = $auth_handler->verify_phone_otp($session_info, $otp_code);
        }

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }

        // For Twilio, we need to create user data
        if ($provider === 'twilio') {
            $firebase_user = array(
                'firebase_uid' => 'phone_' . md5($phone_number),
                'phone_number' => $phone_number,
                'email' => '',
                'email_verified' => false,
                'name' => '',
                'picture' => '',
                'provider_id' => 'phone',
            );
        } else {
            $firebase_user = $result;
        }

        // Get user sync handler
        $user_sync = new Smart_Auth_User_Sync();

        // Sync or create WordPress user
        $wp_user = $user_sync->sync_user($firebase_user, 'phone');
        if (is_wp_error($wp_user)) {
            wp_send_json_error(array('message' => $wp_user->get_error_message()));
        }

        // Log the user in
        wp_set_current_user($wp_user->ID);
        wp_set_auth_cookie($wp_user->ID);

        // Fire action
        do_action('smart_auth_user_authenticated', $wp_user, $firebase_user, 'phone');

        wp_send_json_success(array(
            'message' => __('Phone verification successful.', 'smart-auth'),
            'redirect_url' => apply_filters('smart_auth_redirect_url', home_url(), $wp_user),
        ));
    }

    /**
     * Handle user login
     */
    public function on_user_login($user_login, $user) {
        // Update last login time
        update_user_meta($user->ID, 'last_login', current_time('mysql'));

        // Fire action
        do_action('smart_auth_user_logged_in', $user);
    }

    /**
     * Handle user logout
     */
    public function on_user_logout() {
        // Fire action
        do_action('smart_auth_user_logged_out', wp_get_current_user());
    }
}
