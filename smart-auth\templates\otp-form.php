<?php
/**
 * OTP Form Template
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Default attributes
$defaults = array(
    'title' => __('Phone Verification', 'smart-auth'),
    'description' => __('Enter your phone number to receive a verification code.', 'smart-auth'),
    'phone_placeholder' => __('Enter your phone number', 'smart-auth'),
    'otp_placeholder' => __('Enter verification code', 'smart-auth'),
    'provider' => 'firebase',
    'default_country' => 'US',
    'show_country_selector' => true,
    'phone_validation' => true,
    'otp_length' => 6,
    'enable_resend' => true,
    'resend_cooldown' => 60,
    'auto_submit' => true,
    'redirect_url' => '',
    'form_style' => 'default',
    'css_class' => '',
);

$atts = wp_parse_args($atts, $defaults);

// Sanitize attributes
$title = sanitize_text_field($atts['title']);
$description = sanitize_textarea_field($atts['description']);
$phone_placeholder = sanitize_text_field($atts['phone_placeholder']);
$otp_placeholder = sanitize_text_field($atts['otp_placeholder']);
$provider = sanitize_text_field($atts['provider']);
$default_country = sanitize_text_field($atts['default_country']);
$show_country_selector = filter_var($atts['show_country_selector'], FILTER_VALIDATE_BOOLEAN);
$phone_validation = filter_var($atts['phone_validation'], FILTER_VALIDATE_BOOLEAN);
$otp_length = (int) $atts['otp_length'];
$enable_resend = filter_var($atts['enable_resend'], FILTER_VALIDATE_BOOLEAN);
$resend_cooldown = (int) $atts['resend_cooldown'];
$auto_submit = filter_var($atts['auto_submit'], FILTER_VALIDATE_BOOLEAN);
$redirect_url = esc_url($atts['redirect_url']);
$form_style = sanitize_text_field($atts['form_style']);
$css_class = sanitize_html_class($atts['css_class']);

// Build CSS classes
$form_classes = array(
    'smart-auth-otp-form',
    'smart-auth-form-' . $form_style,
);

if (!empty($css_class)) {
    $form_classes[] = $css_class;
}

$form_class = implode(' ', $form_classes);

// Get supported countries for selector
$countries = array(
    'US' => array('name' => __('United States', 'smart-auth'), 'code' => '+1', 'flag' => '🇺🇸'),
    'CA' => array('name' => __('Canada', 'smart-auth'), 'code' => '+1', 'flag' => '🇨🇦'),
    'GB' => array('name' => __('United Kingdom', 'smart-auth'), 'code' => '+44', 'flag' => '🇬🇧'),
    'AU' => array('name' => __('Australia', 'smart-auth'), 'code' => '+61', 'flag' => '🇦🇺'),
    'DE' => array('name' => __('Germany', 'smart-auth'), 'code' => '+49', 'flag' => '🇩🇪'),
    'FR' => array('name' => __('France', 'smart-auth'), 'code' => '+33', 'flag' => '🇫🇷'),
    'IT' => array('name' => __('Italy', 'smart-auth'), 'code' => '+39', 'flag' => '🇮🇹'),
    'ES' => array('name' => __('Spain', 'smart-auth'), 'code' => '+34', 'flag' => '🇪🇸'),
    'BR' => array('name' => __('Brazil', 'smart-auth'), 'code' => '+55', 'flag' => '🇧🇷'),
    'IN' => array('name' => __('India', 'smart-auth'), 'code' => '+91', 'flag' => '🇮🇳'),
    'JP' => array('name' => __('Japan', 'smart-auth'), 'code' => '+81', 'flag' => '🇯🇵'),
);

// Check if user is already logged in
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    ?>
    <div class="<?php echo esc_attr($form_class); ?>">
        <div class="smart-auth-logged-in">
            <h3><?php esc_html_e('Already Verified', 'smart-auth'); ?></h3>
            <p><?php printf(esc_html__('You are logged in as %s', 'smart-auth'), '<strong>' . esc_html($current_user->display_name) . '</strong>'); ?></p>
            <a href="<?php echo esc_url(wp_logout_url()); ?>" class="smart-auth-button smart-auth-logout-button">
                <?php esc_html_e('Logout', 'smart-auth'); ?>
            </a>
        </div>
    </div>
    <?php
    return;
}
?>

<div class="<?php echo esc_attr($form_class); ?>" 
     data-provider="<?php echo esc_attr($provider); ?>"
     data-default-country="<?php echo esc_attr($default_country); ?>"
     data-otp-length="<?php echo esc_attr($otp_length); ?>"
     data-resend-cooldown="<?php echo esc_attr($resend_cooldown); ?>"
     data-redirect-url="<?php echo esc_attr($redirect_url); ?>"
     <?php if ($phone_validation) : ?>data-phone-validation="true"<?php endif; ?>
     <?php if ($enable_resend) : ?>data-enable-resend="true"<?php endif; ?>
     <?php if ($auto_submit) : ?>data-auto-submit="true"<?php endif; ?>>
    
    <?php if (!empty($title)) : ?>
        <h3 class="smart-auth-otp-title"><?php echo esc_html($title); ?></h3>
    <?php endif; ?>
    
    <?php if (!empty($description)) : ?>
        <p class="smart-auth-otp-description"><?php echo esc_html($description); ?></p>
    <?php endif; ?>
    
    <!-- Phone Input Section -->
    <div class="smart-auth-phone-section">
        <div class="smart-auth-phone-input-wrapper">
            <?php if ($show_country_selector) : ?>
                <select class="smart-auth-country-selector" name="country_code">
                    <?php foreach ($countries as $code => $country) : ?>
                        <option value="<?php echo esc_attr($code); ?>" 
                                data-code="<?php echo esc_attr($country['code']); ?>"
                                <?php selected($default_country, $code); ?>>
                            <?php echo esc_html($country['flag'] . ' ' . $country['code']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            <?php endif; ?>
            
            <input type="tel" 
                   class="smart-auth-input smart-auth-phone-input" 
                   name="phone_number"
                   placeholder="<?php echo esc_attr($phone_placeholder); ?>" 
                   required />
        </div>
        
        <button type="button" class="smart-auth-button smart-auth-send-otp">
            <?php esc_html_e('Send Code', 'smart-auth'); ?>
        </button>
    </div>
    
    <!-- OTP Input Section (initially hidden) -->
    <div class="smart-auth-otp-section" style="display: none;">
        <div class="smart-auth-otp-input-wrapper">
            <input type="text" 
                   class="smart-auth-input smart-auth-otp-input" 
                   name="otp_code"
                   placeholder="<?php echo esc_attr($otp_placeholder); ?>" 
                   maxlength="<?php echo esc_attr($otp_length); ?>"
                   pattern="[0-9]*"
                   inputmode="numeric"
                   autocomplete="one-time-code"
                   required />
        </div>
        
        <button type="button" class="smart-auth-button smart-auth-verify-otp">
            <?php esc_html_e('Verify Code', 'smart-auth'); ?>
        </button>
        
        <?php if ($enable_resend) : ?>
            <div class="smart-auth-resend-section">
                <p class="smart-auth-resend-text">
                    <?php esc_html_e("Didn't receive the code?", 'smart-auth'); ?>
                </p>
                <button type="button" class="smart-auth-resend-button" disabled>
                    <?php printf(esc_html__('Resend in %ds', 'smart-auth'), '<span class="countdown">' . $resend_cooldown . '</span>'); ?>
                </button>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Success Section (initially hidden) -->
    <div class="smart-auth-success-section" style="display: none;">
        <div class="smart-auth-success-icon">✓</div>
        <h4><?php esc_html_e('Verification Successful!', 'smart-auth'); ?></h4>
        <p><?php esc_html_e('You will be redirected shortly...', 'smart-auth'); ?></p>
    </div>
    
    <!-- Hidden fields -->
    <input type="hidden" class="smart-auth-session-info" name="session_info" value="" />
    <input type="hidden" class="smart-auth-formatted-phone" name="formatted_phone" value="" />
</div>

<?php
// Enqueue additional scripts for OTP functionality
wp_enqueue_script(
    'smart-auth-otp-handler',
    SMART_AUTH_PLUGIN_URL . 'assets/js/otp-handler.js',
    array('jquery', 'smart-auth-frontend'),
    SMART_AUTH_VERSION,
    true
);

// Add inline styles for better UX
?>
<style>
.smart-auth-phone-input-wrapper {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.smart-auth-country-selector {
    flex: 0 0 auto;
    min-width: 80px;
    padding: 12px 8px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    font-size: 14px;
}

.smart-auth-phone-input {
    flex: 1;
}

.smart-auth-otp-input-wrapper {
    margin-bottom: 12px;
}

.smart-auth-otp-input {
    text-align: center;
    letter-spacing: 4px;
    font-size: 18px;
    font-weight: 600;
}

.smart-auth-resend-section {
    margin-top: 16px;
    text-align: center;
}

.smart-auth-resend-text {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #666;
}

.smart-auth-resend-button {
    background: none;
    border: none;
    color: #007cba;
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
}

.smart-auth-resend-button:disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: none;
}

.smart-auth-success-section {
    text-align: center;
    padding: 20px;
}

.smart-auth-success-icon {
    font-size: 48px;
    color: #28a745;
    margin-bottom: 16px;
}

.smart-auth-success-section h4 {
    margin: 0 0 8px 0;
    color: #28a745;
}

.smart-auth-success-section p {
    margin: 0;
    color: #666;
}
</style>
