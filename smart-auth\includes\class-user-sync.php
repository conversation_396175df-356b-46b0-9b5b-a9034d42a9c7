<?php
/**
 * User Synchronization Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * User Sync class
 */
class Smart_Auth_User_Sync {

    /**
     * User settings
     *
     * @var array
     */
    private $settings;

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('smart_auth_user_settings', array());
    }

    /**
     * Create or update WordPress user from Firebase data
     *
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider (google, facebook, apple, phone)
     * @return WP_User|WP_Error WordPress user object or error
     */
    public function sync_user($firebase_user, $auth_provider) {
        if (empty($firebase_user['firebase_uid'])) {
            return new WP_Error('missing_firebase_uid', __('Firebase UID is required.', 'smart-auth'));
        }

        // Check if user already exists by Firebase UID
        $existing_user = $this->find_user_by_firebase_uid($firebase_user['firebase_uid']);

        if ($existing_user) {
            // Update existing user
            return $this->update_existing_user($existing_user, $firebase_user, $auth_provider);
        }

        // Check for duplicate accounts by email or phone
        $duplicate_user = $this->find_existing_user(
            isset($firebase_user['email']) ? $firebase_user['email'] : '',
            isset($firebase_user['phone_number']) ? $firebase_user['phone_number'] : ''
        );

        if ($duplicate_user) {
            return $this->handle_duplicate_account($duplicate_user, $firebase_user, $auth_provider);
        }

        // Create new user if auto-creation is enabled
        if ($this->is_auto_create_enabled()) {
            return $this->create_new_user($firebase_user, $auth_provider);
        }

        return new WP_Error('user_creation_disabled', __('User creation is disabled.', 'smart-auth'));
    }

    /**
     * Create new WordPress user
     *
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider
     * @return WP_User|WP_Error WordPress user object or error
     */
    private function create_new_user($firebase_user, $auth_provider) {
        // Prepare user data
        $user_data = $this->prepare_user_data($firebase_user, $auth_provider);

        if (is_wp_error($user_data)) {
            return $user_data;
        }

        // Create user
        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            return $user_id;
        }

        // Update user meta
        $this->update_user_meta($user_id, $firebase_user, $auth_provider);

        // Handle profile picture
        if (!empty($firebase_user['picture']) && $this->is_sync_profile_picture_enabled()) {
            $this->update_profile_picture($user_id, $firebase_user['picture']);
        }

        // Get the created user
        $user = get_user_by('id', $user_id);

        // Fire action for user creation
        do_action('smart_auth_user_created', $user, $firebase_user, $auth_provider);

        return $user;
    }

    /**
     * Update existing WordPress user
     *
     * @param WP_User $user Existing WordPress user
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider
     * @return WP_User|WP_Error WordPress user object or error
     */
    private function update_existing_user($user, $firebase_user, $auth_provider) {
        // Update user meta
        $this->update_user_meta($user->ID, $firebase_user, $auth_provider);

        // Update display name if empty or if sync is enabled
        if (empty($user->display_name) || $this->is_sync_display_name_enabled()) {
            if (!empty($firebase_user['name'])) {
                wp_update_user(array(
                    'ID' => $user->ID,
                    'display_name' => sanitize_text_field($firebase_user['name']),
                ));
            }
        }

        // Update email if verified and sync is enabled
        if ($this->is_sync_email_enabled() &&
            !empty($firebase_user['email']) &&
            !empty($firebase_user['email_verified']) &&
            $firebase_user['email_verified']) {

            wp_update_user(array(
                'ID' => $user->ID,
                'user_email' => sanitize_email($firebase_user['email']),
            ));
        }

        // Handle profile picture
        if (!empty($firebase_user['picture']) && $this->is_sync_profile_picture_enabled()) {
            $this->update_profile_picture($user->ID, $firebase_user['picture']);
        }

        // Fire action for user update
        do_action('smart_auth_user_updated', $user, $firebase_user, $auth_provider);

        return $user;
    }

    /**
     * Handle duplicate account scenario
     *
     * @param WP_User $existing_user Existing WordPress user
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider
     * @return WP_User|WP_Error WordPress user object or error
     */
    private function handle_duplicate_account($existing_user, $firebase_user, $auth_provider) {
        $duplicate_strategy = $this->get_duplicate_account_strategy();

        switch ($duplicate_strategy) {
            case 'merge':
                // Merge Firebase data with existing account
                $this->update_user_meta($existing_user->ID, $firebase_user, $auth_provider);
                return $existing_user;

            case 'error':
                return new WP_Error('duplicate_account', __('An account with this email or phone number already exists.', 'smart-auth'));

            case 'login':
            default:
                // Just log in the existing user
                return $existing_user;
        }
    }

    /**
     * Prepare user data for WordPress user creation
     *
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider
     * @return array|WP_Error User data array or error
     */
    private function prepare_user_data($firebase_user, $auth_provider) {
        $user_data = array();

        // Generate username
        $username = $this->generate_username($firebase_user, $auth_provider);
        if (is_wp_error($username)) {
            return $username;
        }
        $user_data['user_login'] = $username;

        // Set email
        if (!empty($firebase_user['email'])) {
            $user_data['user_email'] = sanitize_email($firebase_user['email']);
        } elseif ($auth_provider === 'phone' && !empty($firebase_user['phone_number'])) {
            // For phone auth, create a placeholder email
            $user_data['user_email'] = $this->generate_placeholder_email($firebase_user['phone_number']);
        } else {
            return new WP_Error('missing_email', __('Email address is required.', 'smart-auth'));
        }

        // Set display name
        if (!empty($firebase_user['name'])) {
            $user_data['display_name'] = sanitize_text_field($firebase_user['name']);
            $user_data['first_name'] = sanitize_text_field($firebase_user['name']);
        } else {
            $user_data['display_name'] = $username;
        }

        // Set user role
        $user_data['role'] = $this->get_default_user_role();

        // Generate password (user won't use it for login)
        $user_data['user_pass'] = wp_generate_password(20, true, true);

        return $user_data;
    }

    /**
     * Update user meta with Firebase data
     *
     * @param int $user_id WordPress user ID
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider
     */
    private function update_user_meta($user_id, $firebase_user, $auth_provider) {
        // Store Firebase UID
        update_user_meta($user_id, 'firebase_uid', sanitize_text_field($firebase_user['firebase_uid']));

        // Store auth provider
        update_user_meta($user_id, 'auth_provider', sanitize_text_field($auth_provider));

        // Store phone number if available
        if (!empty($firebase_user['phone_number'])) {
            update_user_meta($user_id, 'phone_number', sanitize_text_field($firebase_user['phone_number']));
        }

        // Store profile picture URL
        if (!empty($firebase_user['picture'])) {
            update_user_meta($user_id, 'profile_picture_url', esc_url_raw($firebase_user['picture']));
        }

        // Store email verification status
        if (isset($firebase_user['email_verified'])) {
            update_user_meta($user_id, 'email_verified', (bool) $firebase_user['email_verified']);
        }

        // Update last sync time
        update_user_meta($user_id, 'last_firebase_sync', current_time('mysql'));

        // Store custom claims if available
        if (!empty($firebase_user['custom_claims'])) {
            update_user_meta($user_id, 'firebase_custom_claims', $firebase_user['custom_claims']);
        }
    }

    /**
     * Generate unique username
     *
     * @param array $firebase_user Firebase user data
     * @param string $auth_provider Authentication provider
     * @return string|WP_Error Username or error
     */
    private function generate_username($firebase_user, $auth_provider) {
        $base_username = '';

        // Try to use name first
        if (!empty($firebase_user['name'])) {
            $base_username = sanitize_user(strtolower(str_replace(' ', '', $firebase_user['name'])));
        }

        // Fallback to email
        if (empty($base_username) && !empty($firebase_user['email'])) {
            $email_parts = explode('@', $firebase_user['email']);
            $base_username = sanitize_user($email_parts[0]);
        }

        // Fallback to phone number
        if (empty($base_username) && !empty($firebase_user['phone_number'])) {
            $base_username = 'user_' . substr(preg_replace('/[^0-9]/', '', $firebase_user['phone_number']), -8);
        }

        // Final fallback
        if (empty($base_username)) {
            $base_username = 'smart_auth_user';
        }

        // Ensure username is unique
        $username = $base_username;
        $counter = 1;

        while (username_exists($username)) {
            $username = $base_username . '_' . $counter;
            $counter++;

            // Prevent infinite loop
            if ($counter > 1000) {
                return new WP_Error('username_generation_failed', __('Failed to generate unique username.', 'smart-auth'));
            }
        }

        return $username;
    }

    /**
     * Generate placeholder email for phone authentication
     *
     * @param string $phone_number Phone number
     * @return string Placeholder email
     */
    private function generate_placeholder_email($phone_number) {
        $phone_hash = md5($phone_number);
        $domain = parse_url(get_site_url(), PHP_URL_HOST);
        return "phone_{$phone_hash}@{$domain}";
    }

    /**
     * Update user profile picture
     *
     * @param int $user_id WordPress user ID
     * @param string $picture_url Profile picture URL
     * @return bool|WP_Error True on success, error on failure
     */
    public function update_profile_picture($user_id, $picture_url) {
        if (empty($picture_url) || !filter_var($picture_url, FILTER_VALIDATE_URL)) {
            return new WP_Error('invalid_picture_url', __('Invalid picture URL.', 'smart-auth'));
        }

        // Check if we should download and store the image
        if (!$this->is_download_profile_picture_enabled()) {
            // Just store the URL
            update_user_meta($user_id, 'profile_picture_url', esc_url_raw($picture_url));
            return true;
        }

        // Download and attach the image
        $attachment_id = $this->download_and_attach_image($picture_url, $user_id);

        if (is_wp_error($attachment_id)) {
            // Fallback to storing URL
            update_user_meta($user_id, 'profile_picture_url', esc_url_raw($picture_url));
            return $attachment_id;
        }

        // Set as user avatar
        update_user_meta($user_id, 'smart_auth_avatar_attachment_id', $attachment_id);
        update_user_meta($user_id, 'profile_picture_url', esc_url_raw($picture_url));

        return true;
    }

    /**
     * Download and attach image to media library
     *
     * @param string $image_url Image URL
     * @param int $user_id User ID
     * @return int|WP_Error Attachment ID or error
     */
    private function download_and_attach_image($image_url, $user_id) {
        require_once ABSPATH . 'wp-admin/includes/media.php';
        require_once ABSPATH . 'wp-admin/includes/file.php';
        require_once ABSPATH . 'wp-admin/includes/image.php';

        // Download image
        $temp_file = download_url($image_url);

        if (is_wp_error($temp_file)) {
            return $temp_file;
        }

        // Get file info
        $file_info = wp_check_filetype(basename($image_url));
        $file_extension = $file_info['ext'];

        if (!$file_extension) {
            $file_extension = 'jpg'; // Default extension
        }

        // Prepare file array
        $file_array = array(
            'name' => 'profile_picture_' . $user_id . '.' . $file_extension,
            'tmp_name' => $temp_file,
        );

        // Upload file
        $attachment_id = media_handle_sideload($file_array, 0);

        // Clean up temp file
        if (file_exists($temp_file)) {
            unlink($temp_file);
        }

        return $attachment_id;
    }

    /**
     * Check for duplicate accounts
     *
     * @param string $email Email address
     * @param string $phone_number Phone number
     * @return WP_User|null Existing user or null
     */
    public function find_existing_user($email, $phone_number = '') {
        // Check by email first
        if (!empty($email)) {
            $user = get_user_by('email', $email);
            if ($user) {
                return $user;
            }
        }

        // Check by phone number
        if (!empty($phone_number)) {
            $users = get_users(array(
                'meta_key' => 'phone_number',
                'meta_value' => $phone_number,
                'number' => 1,
            ));

            if (!empty($users)) {
                return $users[0];
            }
        }

        return null;
    }

    /**
     * Find user by Firebase UID
     *
     * @param string $firebase_uid Firebase UID
     * @return WP_User|null User or null
     */
    public function find_user_by_firebase_uid($firebase_uid) {
        if (empty($firebase_uid)) {
            return null;
        }

        $users = get_users(array(
            'meta_key' => 'firebase_uid',
            'meta_value' => $firebase_uid,
            'number' => 1,
        ));

        return !empty($users) ? $users[0] : null;
    }

    /**
     * Check if auto user creation is enabled
     *
     * @return bool True if enabled, false otherwise
     */
    private function is_auto_create_enabled() {
        return isset($this->settings['auto_create_users']) ? (bool) $this->settings['auto_create_users'] : true;
    }

    /**
     * Check if profile picture sync is enabled
     *
     * @return bool True if enabled, false otherwise
     */
    private function is_sync_profile_picture_enabled() {
        return isset($this->settings['sync_profile_picture']) ? (bool) $this->settings['sync_profile_picture'] : true;
    }

    /**
     * Check if profile picture download is enabled
     *
     * @return bool True if enabled, false otherwise
     */
    private function is_download_profile_picture_enabled() {
        return isset($this->settings['download_profile_picture']) ? (bool) $this->settings['download_profile_picture'] : false;
    }

    /**
     * Check if display name sync is enabled
     *
     * @return bool True if enabled, false otherwise
     */
    private function is_sync_display_name_enabled() {
        return isset($this->settings['sync_display_name']) ? (bool) $this->settings['sync_display_name'] : true;
    }

    /**
     * Check if email sync is enabled
     *
     * @return bool True if enabled, false otherwise
     */
    private function is_sync_email_enabled() {
        return isset($this->settings['sync_email']) ? (bool) $this->settings['sync_email'] : false;
    }

    /**
     * Get default user role
     *
     * @return string Default user role
     */
    private function get_default_user_role() {
        return isset($this->settings['default_role']) ? $this->settings['default_role'] : 'subscriber';
    }

    /**
     * Get duplicate account handling strategy
     *
     * @return string Strategy (login, merge, error)
     */
    private function get_duplicate_account_strategy() {
        return isset($this->settings['duplicate_strategy']) ? $this->settings['duplicate_strategy'] : 'login';
    }

    /**
     * Get user settings
     *
     * @return array User settings
     */
    public function get_settings() {
        return $this->settings;
    }

    /**
     * Update user settings
     *
     * @param array $settings New settings
     * @return bool True on success, false on failure
     */
    public function update_settings($settings) {
        $this->settings = $settings;
        return update_option('smart_auth_user_settings', $settings);
    }
}
