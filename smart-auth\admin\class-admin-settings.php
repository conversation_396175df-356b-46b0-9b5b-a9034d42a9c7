<?php
/**
 * Admin Settings Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings class
 */
class Smart_Auth_Admin_Settings {
    
    /**
     * Settings page hook suffix
     *
     * @var string
     */
    private $page_hook;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_smart_auth_test_firebase', array($this, 'ajax_test_firebase'));
        add_action('wp_ajax_smart_auth_test_twilio', array($this, 'ajax_test_twilio'));
        add_action('wp_ajax_smart_auth_generate_jwt_secret', array($this, 'ajax_generate_jwt_secret'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $this->page_hook = add_options_page(
            __('Smart Auth Settings', 'smart-auth'),
            __('Smart Auth', 'smart-auth'),
            'manage_options',
            'smart-auth-settings',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our settings page
        if ($hook !== $this->page_hook) {
            return;
        }

        // Enqueue admin CSS
        wp_enqueue_style(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            SMART_AUTH_VERSION
        );

        // Enqueue admin JavaScript
        wp_enqueue_script(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('smart-auth-admin', 'smartAuthAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('smart_auth_admin_nonce'),
            'strings' => array(
                'testing' => __('Testing...', 'smart-auth'),
                'success' => __('Success!', 'smart-auth'),
                'error' => __('Error', 'smart-auth'),
                'connectionSuccess' => __('Connection successful!', 'smart-auth'),
                'connectionFailed' => __('Connection failed. Please check your settings.', 'smart-auth'),
                'secretGenerated' => __('New secret key generated!', 'smart-auth'),
                'copySuccess' => __('Copied to clipboard!', 'smart-auth'),
                'copyError' => __('Failed to copy to clipboard.', 'smart-auth'),
            )
        ));
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // Firebase settings
        register_setting('smart_auth_firebase', 'smart_auth_firebase_settings', array(
            'sanitize_callback' => array($this, 'sanitize_firebase_settings'),
        ));
        
        // Twilio settings
        register_setting('smart_auth_twilio', 'smart_auth_twilio_settings', array(
            'sanitize_callback' => array($this, 'sanitize_twilio_settings'),
        ));
        
        // JWT settings
        register_setting('smart_auth_jwt', 'smart_auth_jwt_settings', array(
            'sanitize_callback' => array($this, 'sanitize_jwt_settings'),
        ));
        
        // User settings
        register_setting('smart_auth_user', 'smart_auth_user_settings', array(
            'sanitize_callback' => array($this, 'sanitize_user_settings'),
        ));
        
        // Security settings
        register_setting('smart_auth_security', 'smart_auth_security_settings', array(
            'sanitize_callback' => array($this, 'sanitize_security_settings'),
        ));
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'firebase';
        ?>
        <div class="wrap smart-auth-admin-wrapper">
            <!-- Header Section -->
            <div class="smart-auth-header">
                <div class="smart-auth-header-content">
                    <div class="smart-auth-header-title">
                        <div class="smart-auth-icon">
                            <span class="dashicons dashicons-shield-alt"></span>
                        </div>
                        <div class="smart-auth-title-text">
                            <h1><?php esc_html_e('Smart Auth', 'smart-auth'); ?></h1>
                            <p class="smart-auth-subtitle"><?php esc_html_e('Complete authentication solution for WordPress', 'smart-auth'); ?></p>
                        </div>
                    </div>
                    <div class="smart-auth-header-actions">
                        <div class="smart-auth-status-overview">
                            <?php $this->render_status_overview(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="smart-auth-nav-container">
                <nav class="smart-auth-nav-tabs" role="tablist" aria-label="<?php esc_attr_e('Smart Auth Settings Navigation', 'smart-auth'); ?>">
                    <a href="?page=smart-auth-settings&tab=firebase"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'firebase' ? 'active' : ''; ?>"
                       data-tab="firebase"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'firebase' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-firebase"
                       id="smart-auth-tab-firebase">
                        <span class="smart-auth-nav-icon dashicons dashicons-admin-network" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Firebase', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('firebase'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=twilio"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'twilio' ? 'active' : ''; ?>"
                       data-tab="twilio"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'twilio' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-twilio"
                       id="smart-auth-tab-twilio">
                        <span class="smart-auth-nav-icon dashicons dashicons-smartphone" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Twilio', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('twilio'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=jwt"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'jwt' ? 'active' : ''; ?>"
                       data-tab="jwt"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'jwt' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-jwt"
                       id="smart-auth-tab-jwt">
                        <span class="smart-auth-nav-icon dashicons dashicons-lock" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('JWT', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('jwt'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=user"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'user' ? 'active' : ''; ?>"
                       data-tab="user"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'user' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-user"
                       id="smart-auth-tab-user">
                        <span class="smart-auth-nav-icon dashicons dashicons-admin-users" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Users', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('user'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=security"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'security' ? 'active' : ''; ?>"
                       data-tab="security"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'security' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-security"
                       id="smart-auth-tab-security">
                        <span class="smart-auth-nav-icon dashicons dashicons-shield" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Security', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('security'); ?></span>
                    </a>
                </nav>
            </div>

            <!-- Main Content Area -->
            <div class="smart-auth-main-content">
                <!-- Firebase Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="firebase"
                     id="smart-auth-panel-firebase"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-firebase"
                     style="<?php echo $active_tab === 'firebase' ? '' : 'display: none;'; ?>">
                    <?php $this->render_firebase_tab(); ?>
                </div>

                <!-- Twilio Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="twilio"
                     id="smart-auth-panel-twilio"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-twilio"
                     style="<?php echo $active_tab === 'twilio' ? '' : 'display: none;'; ?>">
                    <?php $this->render_twilio_tab(); ?>
                </div>

                <!-- JWT Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="jwt"
                     id="smart-auth-panel-jwt"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-jwt"
                     style="<?php echo $active_tab === 'jwt' ? '' : 'display: none;'; ?>">
                    <?php $this->render_jwt_tab(); ?>
                </div>

                <!-- User Settings Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="user"
                     id="smart-auth-panel-user"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-user"
                     style="<?php echo $active_tab === 'user' ? '' : 'display: none;'; ?>">
                    <?php $this->render_user_tab(); ?>
                </div>

                <!-- Security Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="security"
                     id="smart-auth-panel-security"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-security"
                     style="<?php echo $active_tab === 'security' ? '' : 'display: none;'; ?>">
                    <?php $this->render_security_tab(); ?>
                </div>
            </div>

            <!-- Live region for announcements -->
            <div id="smart-auth-live-region" class="smart-auth-live-region" aria-live="polite" aria-atomic="true"></div>

            <!-- Hidden nonce field for AJAX requests -->
            <input type="hidden" id="smart_auth_admin_nonce" value="<?php echo wp_create_nonce('smart_auth_admin_nonce'); ?>" />
        </div>
        <?php
    }

    /**
     * Render status overview in header
     */
    private function render_status_overview() {
        $firebase_status = $this->get_config_status('firebase');
        $twilio_status = $this->get_config_status('twilio');
        $jwt_status = $this->get_config_status('jwt');

        $total_configured = 0;
        $total_services = 3;

        if ($firebase_status === 'configured') $total_configured++;
        if ($twilio_status === 'configured') $total_configured++;
        if ($jwt_status === 'configured') $total_configured++;

        $percentage = round(($total_configured / $total_services) * 100);
        ?>
        <div class="smart-auth-status-summary">
            <div class="smart-auth-status-circle">
                <svg class="smart-auth-progress-ring" width="60" height="60">
                    <circle class="smart-auth-progress-ring-circle" stroke="#e1e5e9" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                    <circle class="smart-auth-progress-ring-progress" stroke="#007cba" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"
                            style="stroke-dasharray: <?php echo 163.36; ?>; stroke-dashoffset: <?php echo 163.36 - (163.36 * $percentage / 100); ?>;"/>
                </svg>
                <div class="smart-auth-status-text">
                    <span class="smart-auth-status-percentage"><?php echo $percentage; ?>%</span>
                    <span class="smart-auth-status-label"><?php esc_html_e('Setup', 'smart-auth'); ?></span>
                </div>
            </div>
            <div class="smart-auth-status-details">
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $firebase_status; ?>"></span>
                    <?php esc_html_e('Firebase', 'smart-auth'); ?>
                </div>
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $twilio_status; ?>"></span>
                    <?php esc_html_e('Twilio', 'smart-auth'); ?>
                </div>
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $jwt_status; ?>"></span>
                    <?php esc_html_e('JWT', 'smart-auth'); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get configuration status for a service
     */
    private function get_config_status($service) {
        switch ($service) {
            case 'firebase':
                $settings = get_option('smart_auth_firebase_settings', array());
                $required_fields = array('project_id', 'api_key', 'auth_domain');
                break;
            case 'twilio':
                $settings = get_option('smart_auth_twilio_settings', array());
                $required_fields = array('account_sid', 'auth_token', 'verify_service_sid');
                break;
            case 'jwt':
                $settings = get_option('smart_auth_jwt_settings', array());
                $required_fields = array('secret_key');
                break;
            case 'user':
                $settings = get_option('smart_auth_user_settings', array());
                return 'configured'; // User settings are always considered configured
            case 'security':
                $settings = get_option('smart_auth_security_settings', array());
                return 'configured'; // Security settings are always considered configured
            default:
                return 'not-configured';
        }

        foreach ($required_fields as $field) {
            if (empty($settings[$field])) {
                return 'not-configured';
            }
        }

        return 'configured';
    }
    
    /**
     * Render Firebase settings tab
     */
    private function render_firebase_tab() {
        $settings = get_option('smart_auth_firebase_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Firebase Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure Firebase Authentication for social login providers like Google, Facebook, and Apple.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-cards-grid">
            <!-- Configuration Card -->
            <div class="smart-auth-card">
                <div class="smart-auth-card-header">
                    <h3><?php esc_html_e('Firebase Settings', 'smart-auth'); ?></h3>
                    <div class="smart-auth-card-status <?php echo $this->get_config_status('firebase'); ?>">
                        <?php echo $this->get_config_status('firebase') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                    </div>
                </div>
                <div class="smart-auth-card-body">
                    <form method="post" action="options.php" class="smart-auth-form">
                        <?php
                        settings_fields('smart_auth_firebase');
                        do_settings_sections('smart_auth_firebase');
                        ?>

                        <div class="smart-auth-form-grid">
                            <div class="smart-auth-form-group">
                                <label for="firebase_project_id" class="smart-auth-label">
                                    <?php esc_html_e('Project ID', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <input type="text"
                                       id="firebase_project_id"
                                       name="smart_auth_firebase_settings[project_id]"
                                       value="<?php echo esc_attr(isset($settings['project_id']) ? $settings['project_id'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="your-project-id" />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase project ID from the Firebase console', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="firebase_api_key" class="smart-auth-label">
                                    <?php esc_html_e('Web API Key', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <input type="text"
                                       id="firebase_api_key"
                                       name="smart_auth_firebase_settings[api_key]"
                                       value="<?php echo esc_attr(isset($settings['api_key']) ? $settings['api_key'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="AIzaSyC..." />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase Web API key from Project Settings', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="firebase_auth_domain" class="smart-auth-label">
                                    <?php esc_html_e('Auth Domain', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <input type="text"
                                       id="firebase_auth_domain"
                                       name="smart_auth_firebase_settings[auth_domain]"
                                       value="<?php echo esc_attr(isset($settings['auth_domain']) ? $settings['auth_domain'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="your-project-id.firebaseapp.com" />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase authentication domain', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="firebase_storage_bucket" class="smart-auth-label">
                                    <?php esc_html_e('Storage Bucket', 'smart-auth'); ?>
                                </label>
                                <input type="text"
                                       id="firebase_storage_bucket"
                                       name="smart_auth_firebase_settings[storage_bucket]"
                                       value="<?php echo esc_attr(isset($settings['storage_bucket']) ? $settings['storage_bucket'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="your-project-id.appspot.com" />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase storage bucket (optional)', 'smart-auth'); ?></p>
                            </div>
                        </div>

                        <div class="smart-auth-form-actions">
                            <button type="button" id="test-firebase-connection" class="smart-auth-button smart-auth-button-secondary">
                                <span class="dashicons dashicons-admin-network"></span>
                                <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                            </button>
                            <div id="firebase-test-result" class="smart-auth-test-result"></div>
                        </div>

                        <div class="smart-auth-card-footer">
                            <?php submit_button(__('Save Firebase Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="smart-auth-card smart-auth-card-help">
                <div class="smart-auth-card-header">
                    <h3><?php esc_html_e('Setup Guide', 'smart-auth'); ?></h3>
                </div>
                <div class="smart-auth-card-body">
                    <div class="smart-auth-help-steps">
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">1</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Create Firebase Project', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('Go to the Firebase Console and create a new project or select an existing one.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">2</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Enable Authentication', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('In the Firebase console, go to Authentication > Sign-in method and enable your desired providers.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">3</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Get Configuration', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('Go to Project Settings > General and copy your web app configuration details.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="smart-auth-help-links">
                        <a href="https://console.firebase.google.com/" target="_blank" class="smart-auth-help-link">
                            <span class="dashicons dashicons-external"></span>
                            <?php esc_html_e('Firebase Console', 'smart-auth'); ?>
                        </a>
                        <a href="https://firebase.google.com/docs/auth" target="_blank" class="smart-auth-help-link">
                            <span class="dashicons dashicons-book"></span>
                            <?php esc_html_e('Documentation', 'smart-auth'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render Twilio settings tab
     */
    private function render_twilio_tab() {
        $settings = get_option('smart_auth_twilio_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Twilio Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure Twilio for SMS-based phone number verification and two-factor authentication.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-cards-grid">
            <!-- Configuration Card -->
            <div class="smart-auth-card">
                <div class="smart-auth-card-header">
                    <h3><?php esc_html_e('Twilio Settings', 'smart-auth'); ?></h3>
                    <div class="smart-auth-card-status <?php echo $this->get_config_status('twilio'); ?>">
                        <?php echo $this->get_config_status('twilio') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                    </div>
                </div>
                <div class="smart-auth-card-body">
                    <form method="post" action="options.php" class="smart-auth-form">
                        <?php
                        settings_fields('smart_auth_twilio');
                        do_settings_sections('smart_auth_twilio');
                        ?>

                        <div class="smart-auth-form-grid">
                            <div class="smart-auth-form-group">
                                <label for="twilio_account_sid" class="smart-auth-label">
                                    <?php esc_html_e('Account SID', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <input type="text"
                                       id="twilio_account_sid"
                                       name="smart_auth_twilio_settings[account_sid]"
                                       value="<?php echo esc_attr(isset($settings['account_sid']) ? $settings['account_sid'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Twilio Account SID from the Twilio Console', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="twilio_auth_token" class="smart-auth-label">
                                    <?php esc_html_e('Auth Token', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <input type="password"
                                       id="twilio_auth_token"
                                       name="smart_auth_twilio_settings[auth_token]"
                                       value="<?php echo esc_attr(isset($settings['auth_token']) ? $settings['auth_token'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="Your auth token" />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Twilio Auth Token (keep this secure)', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="twilio_verify_service_sid" class="smart-auth-label">
                                    <?php esc_html_e('Verify Service SID', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <input type="text"
                                       id="twilio_verify_service_sid"
                                       name="smart_auth_twilio_settings[verify_service_sid]"
                                       value="<?php echo esc_attr(isset($settings['verify_service_sid']) ? $settings['verify_service_sid'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="VAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
                                <p class="smart-auth-help-text"><?php esc_html_e('Your Twilio Verify Service SID for OTP verification', 'smart-auth'); ?></p>
                            </div>
                            <div class="smart-auth-form-group">
                                <label for="twilio_default_region" class="smart-auth-label">
                                    <?php esc_html_e('Default Region', 'smart-auth'); ?>
                                </label>
                                <select id="twilio_default_region" name="smart_auth_twilio_settings[default_region]" class="smart-auth-select">
                                    <?php
                                    $regions = array(
                                        'US' => __('United States', 'smart-auth'),
                                        'CA' => __('Canada', 'smart-auth'),
                                        'GB' => __('United Kingdom', 'smart-auth'),
                                        'AU' => __('Australia', 'smart-auth'),
                                        'DE' => __('Germany', 'smart-auth'),
                                        'FR' => __('France', 'smart-auth'),
                                        'IT' => __('Italy', 'smart-auth'),
                                        'ES' => __('Spain', 'smart-auth'),
                                        'BR' => __('Brazil', 'smart-auth'),
                                        'IN' => __('India', 'smart-auth'),
                                        'JP' => __('Japan', 'smart-auth'),
                                    );
                                    $selected_region = isset($settings['default_region']) ? $settings['default_region'] : 'US';
                                    foreach ($regions as $code => $name) {
                                        echo '<option value="' . esc_attr($code) . '"' . selected($selected_region, $code, false) . '>' . esc_html($name) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="smart-auth-help-text"><?php esc_html_e('Default region for phone number parsing and formatting', 'smart-auth'); ?></p>
                            </div>
                        </div>

                        <div class="smart-auth-form-actions">
                            <button type="button" id="test-twilio-connection" class="smart-auth-button smart-auth-button-secondary">
                                <span class="dashicons dashicons-smartphone"></span>
                                <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                            </button>
                            <div id="twilio-test-result" class="smart-auth-test-result"></div>
                        </div>

                        <div class="smart-auth-card-footer">
                            <?php submit_button(__('Save Twilio Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="smart-auth-card smart-auth-card-help">
                <div class="smart-auth-card-header">
                    <h3><?php esc_html_e('Setup Guide', 'smart-auth'); ?></h3>
                </div>
                <div class="smart-auth-card-body">
                    <div class="smart-auth-help-steps">
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">1</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Create Twilio Account', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('Sign up for a Twilio account and verify your phone number.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">2</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Create Verify Service', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('In the Twilio Console, go to Verify > Services and create a new service.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">3</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Get Credentials', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('Copy your Account SID, Auth Token, and Verify Service SID from the console.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="smart-auth-help-links">
                        <a href="https://console.twilio.com/" target="_blank" class="smart-auth-help-link">
                            <span class="dashicons dashicons-external"></span>
                            <?php esc_html_e('Twilio Console', 'smart-auth'); ?>
                        </a>
                        <a href="https://www.twilio.com/docs/verify" target="_blank" class="smart-auth-help-link">
                            <span class="dashicons dashicons-book"></span>
                            <?php esc_html_e('Documentation', 'smart-auth'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render JWT settings tab
     */
    private function render_jwt_tab() {
        $settings = get_option('smart_auth_jwt_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('JWT Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure JSON Web Token settings for secure API authentication and session management.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-cards-grid">
            <!-- Configuration Card -->
            <div class="smart-auth-card">
                <div class="smart-auth-card-header">
                    <h3><?php esc_html_e('JWT Settings', 'smart-auth'); ?></h3>
                    <div class="smart-auth-card-status <?php echo $this->get_config_status('jwt'); ?>">
                        <?php echo $this->get_config_status('jwt') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                    </div>
                </div>
                <div class="smart-auth-card-body">
                    <form method="post" action="options.php" class="smart-auth-form">
                        <?php
                        settings_fields('smart_auth_jwt');
                        do_settings_sections('smart_auth_jwt');
                        ?>

                        <div class="smart-auth-form-grid">
                            <div class="smart-auth-form-group">
                                <label for="jwt_secret_key" class="smart-auth-label">
                                    <?php esc_html_e('Secret Key', 'smart-auth'); ?>
                                    <span class="smart-auth-required">*</span>
                                </label>
                                <div style="display: flex; gap: 8px; align-items: flex-start;">
                                    <input type="password"
                                           id="jwt_secret_key"
                                           name="smart_auth_jwt_settings[secret_key]"
                                           value="<?php echo esc_attr(isset($settings['secret_key']) ? $settings['secret_key'] : ''); ?>"
                                           class="smart-auth-input"
                                           readonly
                                           style="flex: 1;" />
                                    <button type="button" id="generate-jwt-secret" class="smart-auth-button smart-auth-button-secondary">
                                        <span class="dashicons dashicons-admin-network"></span>
                                        <?php esc_html_e('Generate', 'smart-auth'); ?>
                                    </button>
                                </div>
                                <p class="smart-auth-help-text">
                                    <?php esc_html_e('Secret key for JWT token signing. Keep this secure and never share it.', 'smart-auth'); ?>
                                    <?php if (defined('JWT_AUTH_SECRET_KEY')) : ?>
                                        <br><strong><?php esc_html_e('Note: JWT Authentication plugin detected. Its secret key will be used if this field is empty.', 'smart-auth'); ?></strong>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="smart-auth-form-group">
                                <label for="jwt_expiration" class="smart-auth-label">
                                    <?php esc_html_e('Token Expiration', 'smart-auth'); ?>
                                </label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="number"
                                           id="jwt_expiration"
                                           name="smart_auth_jwt_settings[expiration]"
                                           value="<?php echo esc_attr(isset($settings['expiration']) ? $settings['expiration'] / HOUR_IN_SECONDS : 24); ?>"
                                           min="1" max="168"
                                           class="smart-auth-input"
                                           style="width: 100px;" />
                                    <span><?php esc_html_e('hours', 'smart-auth'); ?></span>
                                </div>
                                <p class="smart-auth-help-text"><?php esc_html_e('How long JWT tokens remain valid (1-168 hours)', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="jwt_refresh_window" class="smart-auth-label">
                                    <?php esc_html_e('Refresh Window', 'smart-auth'); ?>
                                </label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="number"
                                           id="jwt_refresh_window"
                                           name="smart_auth_jwt_settings[refresh_window]"
                                           value="<?php echo esc_attr(isset($settings['refresh_window']) ? $settings['refresh_window'] / DAY_IN_SECONDS : 7); ?>"
                                           min="1" max="30"
                                           class="smart-auth-input"
                                           style="width: 100px;" />
                                    <span><?php esc_html_e('days', 'smart-auth'); ?></span>
                                </div>
                                <p class="smart-auth-help-text"><?php esc_html_e('How long expired tokens can be refreshed (1-30 days)', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label for="jwt_algorithm" class="smart-auth-label">
                                    <?php esc_html_e('Algorithm', 'smart-auth'); ?>
                                </label>
                                <select id="jwt_algorithm" name="smart_auth_jwt_settings[algorithm]" class="smart-auth-select">
                                    <?php
                                    $algorithms = array(
                                        'HS256' => 'HS256 (HMAC SHA-256)',
                                        'HS384' => 'HS384 (HMAC SHA-384)',
                                        'HS512' => 'HS512 (HMAC SHA-512)',
                                    );
                                    $selected_algorithm = isset($settings['algorithm']) ? $settings['algorithm'] : 'HS256';
                                    foreach ($algorithms as $value => $label) {
                                        echo '<option value="' . esc_attr($value) . '"' . selected($selected_algorithm, $value, false) . '>' . esc_html($label) . '</option>';
                                    }
                                    ?>
                                </select>
                                <p class="smart-auth-help-text"><?php esc_html_e('JWT signing algorithm for token security', 'smart-auth'); ?></p>
                            </div>

                            <div class="smart-auth-form-group">
                                <label class="smart-auth-label">
                                    <?php esc_html_e('Security Options', 'smart-auth'); ?>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
                                    <input type="checkbox"
                                           name="smart_auth_jwt_settings[enable_token_storage]"
                                           value="1"
                                           <?php checked(isset($settings['enable_token_storage']) ? $settings['enable_token_storage'] : false); ?> />
                                    <?php esc_html_e('Store token hashes for additional security', 'smart-auth'); ?>
                                </label>
                                <p class="smart-auth-help-text"><?php esc_html_e('Stores token hashes in database for validation. Slightly reduces performance but increases security.', 'smart-auth'); ?></p>
                            </div>
                        </div>

                        <div class="smart-auth-card-footer">
                            <?php submit_button(__('Save JWT Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="smart-auth-card smart-auth-card-help">
                <div class="smart-auth-card-header">
                    <h3><?php esc_html_e('JWT Information', 'smart-auth'); ?></h3>
                </div>
                <div class="smart-auth-card-body">
                    <div class="smart-auth-help-steps">
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">1</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Generate Secret Key', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('Click "Generate" to create a secure random secret key for JWT signing.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">2</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Configure Expiration', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('Set appropriate token expiration times based on your security requirements.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                        <div class="smart-auth-help-step">
                            <div class="smart-auth-step-number">3</div>
                            <div class="smart-auth-step-content">
                                <h4><?php esc_html_e('Choose Algorithm', 'smart-auth'); ?></h4>
                                <p><?php esc_html_e('HS256 is recommended for most use cases. Use stronger algorithms for high-security applications.', 'smart-auth'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="smart-auth-help-links">
                        <a href="https://jwt.io/" target="_blank" class="smart-auth-help-link">
                            <span class="dashicons dashicons-external"></span>
                            <?php esc_html_e('JWT.io', 'smart-auth'); ?>
                        </a>
                        <a href="https://tools.ietf.org/html/rfc7519" target="_blank" class="smart-auth-help-link">
                            <span class="dashicons dashicons-book"></span>
                            <?php esc_html_e('JWT Specification', 'smart-auth'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render User settings tab
     */
    private function render_user_tab() {
        $settings = get_option('smart_auth_user_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('User Settings', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure how new users are created and managed when they authenticate through Smart Auth.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('User Management', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_user');
                    do_settings_sections('smart_auth_user');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <label class="smart-auth-label">
                                <?php esc_html_e('User Creation', 'smart-auth'); ?>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
                                <input type="checkbox"
                                       name="smart_auth_user_settings[auto_create_users]"
                                       value="1"
                                       <?php checked(isset($settings['auto_create_users']) ? $settings['auto_create_users'] : true); ?> />
                                <?php esc_html_e('Automatically create WordPress users for new Firebase users', 'smart-auth'); ?>
                            </label>
                            <p class="smart-auth-help-text"><?php esc_html_e('If disabled, only existing WordPress users can log in.', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="user_default_role" class="smart-auth-label">
                                <?php esc_html_e('Default User Role', 'smart-auth'); ?>
                            </label>
                            <select id="user_default_role" name="smart_auth_user_settings[default_role]" class="smart-auth-select">
                                <?php
                                $roles = get_editable_roles();
                                $selected_role = isset($settings['default_role']) ? $settings['default_role'] : 'subscriber';
                                foreach ($roles as $role_name => $role_info) {
                                    echo '<option value="' . esc_attr($role_name) . '"' . selected($selected_role, $role_name, false) . '>' . esc_html($role_info['name']) . '</option>';
                                }
                                ?>
                            </select>
                            <p class="smart-auth-help-text"><?php esc_html_e('Default role assigned to new users', 'smart-auth'); ?></p>
                        </div>
                        <div class="smart-auth-form-group">
                            <label for="user_duplicate_strategy" class="smart-auth-label">
                                <?php esc_html_e('Duplicate Account Strategy', 'smart-auth'); ?>
                            </label>
                            <select id="user_duplicate_strategy" name="smart_auth_user_settings[duplicate_strategy]" class="smart-auth-select">
                                <option value="login" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'login'); ?>>
                                    <?php esc_html_e('Login to existing account', 'smart-auth'); ?>
                                </option>
                                <option value="merge" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'merge'); ?>>
                                    <?php esc_html_e('Merge Firebase data with existing account', 'smart-auth'); ?>
                                </option>
                                <option value="error" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'error'); ?>>
                                    <?php esc_html_e('Show error message', 'smart-auth'); ?>
                                </option>
                            </select>
                            <p class="smart-auth-help-text"><?php esc_html_e('What to do when a user tries to register with an email/phone that already exists', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label class="smart-auth-label">
                                <?php esc_html_e('Profile Synchronization', 'smart-auth'); ?>
                            </label>
                            <div style="display: flex; flex-direction: column; gap: 8px; margin-top: 8px;">
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[sync_display_name]"
                                           value="1"
                                           <?php checked(isset($settings['sync_display_name']) ? $settings['sync_display_name'] : true); ?> />
                                    <?php esc_html_e('Sync display name from Firebase', 'smart-auth'); ?>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[sync_email]"
                                           value="1"
                                           <?php checked(isset($settings['sync_email']) ? $settings['sync_email'] : false); ?> />
                                    <?php esc_html_e('Sync email from Firebase (only if verified)', 'smart-auth'); ?>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[sync_profile_picture]"
                                           value="1"
                                           <?php checked(isset($settings['sync_profile_picture']) ? $settings['sync_profile_picture'] : true); ?> />
                                    <?php esc_html_e('Sync profile picture from Firebase', 'smart-auth'); ?>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[download_profile_picture]"
                                           value="1"
                                           <?php checked(isset($settings['download_profile_picture']) ? $settings['download_profile_picture'] : false); ?> />
                                    <?php esc_html_e('Download and store profile pictures locally', 'smart-auth'); ?>
                                </label>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('Choose which user data to synchronize from Firebase', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save User Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Render Security settings tab
     */
    private function render_security_tab() {
        $settings = get_option('smart_auth_security_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Security Settings', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure security options and access controls for Smart Auth.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Security Options', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_security');
                    do_settings_sections('smart_auth_security');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <label class="smart-auth-label">
                                <?php esc_html_e('Rate Limiting', 'smart-auth'); ?>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
                                <input type="checkbox"
                                       name="smart_auth_security_settings[enable_rate_limiting]"
                                       value="1"
                                       <?php checked(isset($settings['enable_rate_limiting']) ? $settings['enable_rate_limiting'] : true); ?> />
                                <?php esc_html_e('Enable rate limiting for authentication attempts', 'smart-auth'); ?>
                            </label>
                            <p class="smart-auth-help-text"><?php esc_html_e('Prevents brute force attacks by limiting authentication attempts.', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label class="smart-auth-label">
                                <?php esc_html_e('Session Security', 'smart-auth'); ?>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
                                <input type="checkbox"
                                       name="smart_auth_security_settings[secure_sessions]"
                                       value="1"
                                       <?php checked(isset($settings['secure_sessions']) ? $settings['secure_sessions'] : true); ?> />
                                <?php esc_html_e('Use secure session handling', 'smart-auth'); ?>
                            </label>
                            <p class="smart-auth-help-text"><?php esc_html_e('Enhances session security with additional validation.', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save Security Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }
                        </fieldset>
                        <p class="description"><?php esc_html_e('Choose which Firebase data to store in WordPress user meta', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Render Security settings tab
     */
    private function render_security_tab() {
        $settings = get_option('smart_auth_security_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_security');
            do_settings_sections('smart_auth_security');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Rate Limiting', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <?php esc_html_e('Max attempts:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[rate_limit_attempts]"
                                       value="<?php echo esc_attr(isset($settings['rate_limit_attempts']) ? $settings['rate_limit_attempts'] : 3); ?>"
                                       min="1" max="10" class="small-text" />
                            </label><br>
                            <label>
                                <?php esc_html_e('Time window:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[rate_limit_window]"
                                       value="<?php echo esc_attr(isset($settings['rate_limit_window']) ? $settings['rate_limit_window'] / 60 : 15); ?>"
                                       min="5" max="60" class="small-text" />
                                <?php esc_html_e('minutes', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Limit OTP requests per IP address', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('OTP Settings', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <?php esc_html_e('Resend cooldown:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[otp_resend_cooldown]"
                                       value="<?php echo esc_attr(isset($settings['otp_resend_cooldown']) ? $settings['otp_resend_cooldown'] : 60); ?>"
                                       min="30" max="300" class="small-text" />
                                <?php esc_html_e('seconds', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('OTP expiration:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[otp_expiration]"
                                       value="<?php echo esc_attr(isset($settings['otp_expiration']) ? $settings['otp_expiration'] / 60 : 10); ?>"
                                       min="5" max="30" class="small-text" />
                                <?php esc_html_e('minutes', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('OTP security settings', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Failed Login Protection', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_failed_login_protection]" value="1"
                                       <?php checked(isset($settings['enable_failed_login_protection']) ? $settings['enable_failed_login_protection'] : true); ?> />
                                <?php esc_html_e('Enable failed login protection', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('Max failed attempts:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[max_failed_attempts]"
                                       value="<?php echo esc_attr(isset($settings['max_failed_attempts']) ? $settings['max_failed_attempts'] : 5); ?>"
                                       min="3" max="20" class="small-text" />
                            </label><br>
                            <label>
                                <?php esc_html_e('Lockout duration:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[lockout_duration]"
                                       value="<?php echo esc_attr(isset($settings['lockout_duration']) ? $settings['lockout_duration'] / 60 : 30); ?>"
                                       min="15" max="1440" class="small-text" />
                                <?php esc_html_e('minutes', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Protect against brute force attacks', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('IP Blocking', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_ip_blocking]" value="1"
                                       <?php checked(isset($settings['enable_ip_blocking']) ? $settings['enable_ip_blocking'] : false); ?> />
                                <?php esc_html_e('Enable IP blocking', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('Blocked IPs (one per line):', 'smart-auth'); ?><br>
                                <textarea name="smart_auth_security_settings[blocked_ips]" rows="5" cols="50" class="large-text"><?php echo esc_textarea(isset($settings['blocked_ips']) ? $settings['blocked_ips'] : ''); ?></textarea>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Block specific IP addresses from authentication', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('CAPTCHA Integration', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_captcha]" value="1"
                                       <?php checked(isset($settings['enable_captcha']) ? $settings['enable_captcha'] : false); ?> />
                                <?php esc_html_e('Enable CAPTCHA for phone authentication', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('CAPTCHA provider:', 'smart-auth'); ?>
                                <select name="smart_auth_security_settings[captcha_provider]">
                                    <option value="recaptcha" <?php selected(isset($settings['captcha_provider']) ? $settings['captcha_provider'] : 'recaptcha', 'recaptcha'); ?>>
                                        <?php esc_html_e('Google reCAPTCHA', 'smart-auth'); ?>
                                    </option>
                                    <option value="hcaptcha" <?php selected(isset($settings['captcha_provider']) ? $settings['captcha_provider'] : 'recaptcha', 'hcaptcha'); ?>>
                                        <?php esc_html_e('hCaptcha', 'smart-auth'); ?>
                                    </option>
                                </select>
                            </label><br>
                            <label>
                                <?php esc_html_e('Site key:', 'smart-auth'); ?>
                                <input type="text" name="smart_auth_security_settings[captcha_site_key]"
                                       value="<?php echo esc_attr(isset($settings['captcha_site_key']) ? $settings['captcha_site_key'] : ''); ?>"
                                       class="regular-text" />
                            </label><br>
                            <label>
                                <?php esc_html_e('Secret key:', 'smart-auth'); ?>
                                <input type="password" name="smart_auth_security_settings[captcha_secret_key]"
                                       value="<?php echo esc_attr(isset($settings['captcha_secret_key']) ? $settings['captcha_secret_key'] : ''); ?>"
                                       class="regular-text" />
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Add CAPTCHA verification to prevent automated attacks', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Security Headers', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_security_headers]" value="1"
                                       <?php checked(isset($settings['enable_security_headers']) ? $settings['enable_security_headers'] : true); ?> />
                                <?php esc_html_e('Enable security headers', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_csrf_protection]" value="1"
                                       <?php checked(isset($settings['enable_csrf_protection']) ? $settings['enable_csrf_protection'] : true); ?> />
                                <?php esc_html_e('Enable CSRF protection', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_https_only]" value="1"
                                       <?php checked(isset($settings['enable_https_only']) ? $settings['enable_https_only'] : true); ?> />
                                <?php esc_html_e('Require HTTPS for authentication', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Additional security measures', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Sanitize Firebase settings
     */
    public function sanitize_firebase_settings($input) {
        $sanitized = array();
        
        if (isset($input['project_id'])) {
            $sanitized['project_id'] = sanitize_text_field($input['project_id']);
        }
        
        if (isset($input['api_key'])) {
            $sanitized['api_key'] = sanitize_text_field($input['api_key']);
        }
        
        if (isset($input['auth_domain'])) {
            $sanitized['auth_domain'] = sanitize_text_field($input['auth_domain']);
        }
        
        if (isset($input['storage_bucket'])) {
            $sanitized['storage_bucket'] = sanitize_text_field($input['storage_bucket']);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize Twilio settings
     */
    public function sanitize_twilio_settings($input) {
        $sanitized = array();

        if (isset($input['account_sid'])) {
            $sanitized['account_sid'] = sanitize_text_field($input['account_sid']);
        }

        if (isset($input['auth_token'])) {
            $sanitized['auth_token'] = sanitize_text_field($input['auth_token']);
        }

        if (isset($input['verify_service_sid'])) {
            $sanitized['verify_service_sid'] = sanitize_text_field($input['verify_service_sid']);
        }

        if (isset($input['default_region'])) {
            $sanitized['default_region'] = sanitize_text_field($input['default_region']);
        }

        return $sanitized;
    }

    /**
     * Sanitize JWT settings
     */
    public function sanitize_jwt_settings($input) {
        $sanitized = array();

        if (isset($input['secret_key'])) {
            $sanitized['secret_key'] = sanitize_text_field($input['secret_key']);
        }

        if (isset($input['expiration'])) {
            $hours = (int) $input['expiration'];
            $sanitized['expiration'] = max(1, min(168, $hours)) * HOUR_IN_SECONDS;
        }

        if (isset($input['refresh_window'])) {
            $days = (int) $input['refresh_window'];
            $sanitized['refresh_window'] = max(1, min(30, $days)) * DAY_IN_SECONDS;
        }

        if (isset($input['algorithm'])) {
            $allowed_algorithms = array('HS256', 'HS384', 'HS512');
            $sanitized['algorithm'] = in_array($input['algorithm'], $allowed_algorithms) ? $input['algorithm'] : 'HS256';
        }

        if (isset($input['enable_token_storage'])) {
            $sanitized['enable_token_storage'] = (bool) $input['enable_token_storage'];
        }

        return $sanitized;
    }

    /**
     * Sanitize user settings
     */
    public function sanitize_user_settings($input) {
        $sanitized = array();

        if (isset($input['auto_create_users'])) {
            $sanitized['auto_create_users'] = (bool) $input['auto_create_users'];
        }

        if (isset($input['default_role'])) {
            $roles = get_editable_roles();
            $sanitized['default_role'] = array_key_exists($input['default_role'], $roles) ? $input['default_role'] : 'subscriber';
        }

        if (isset($input['duplicate_strategy'])) {
            $allowed_strategies = array('login', 'merge', 'error');
            $sanitized['duplicate_strategy'] = in_array($input['duplicate_strategy'], $allowed_strategies) ? $input['duplicate_strategy'] : 'login';
        }

        // Profile sync options
        $sync_options = array('sync_display_name', 'sync_email', 'sync_profile_picture', 'download_profile_picture');
        foreach ($sync_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        // User meta storage options
        $meta_options = array('store_firebase_uid', 'store_auth_provider', 'store_phone_number', 'store_custom_claims');
        foreach ($meta_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize security settings
     */
    public function sanitize_security_settings($input) {
        $sanitized = array();

        if (isset($input['rate_limit_attempts'])) {
            $sanitized['rate_limit_attempts'] = max(1, min(10, (int) $input['rate_limit_attempts']));
        }

        if (isset($input['rate_limit_window'])) {
            $minutes = max(5, min(60, (int) $input['rate_limit_window']));
            $sanitized['rate_limit_window'] = $minutes * 60;
        }

        if (isset($input['otp_resend_cooldown'])) {
            $sanitized['otp_resend_cooldown'] = max(30, min(300, (int) $input['otp_resend_cooldown']));
        }

        if (isset($input['otp_expiration'])) {
            $minutes = max(5, min(30, (int) $input['otp_expiration']));
            $sanitized['otp_expiration'] = $minutes * 60;
        }

        if (isset($input['enable_failed_login_protection'])) {
            $sanitized['enable_failed_login_protection'] = (bool) $input['enable_failed_login_protection'];
        }

        if (isset($input['max_failed_attempts'])) {
            $sanitized['max_failed_attempts'] = max(3, min(20, (int) $input['max_failed_attempts']));
        }

        if (isset($input['lockout_duration'])) {
            $minutes = max(15, min(1440, (int) $input['lockout_duration']));
            $sanitized['lockout_duration'] = $minutes * 60;
        }

        if (isset($input['enable_ip_blocking'])) {
            $sanitized['enable_ip_blocking'] = (bool) $input['enable_ip_blocking'];
        }

        if (isset($input['blocked_ips'])) {
            $ips = explode("\n", $input['blocked_ips']);
            $valid_ips = array();
            foreach ($ips as $ip) {
                $ip = trim($ip);
                if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP)) {
                    $valid_ips[] = $ip;
                }
            }
            $sanitized['blocked_ips'] = implode("\n", $valid_ips);
        }

        if (isset($input['enable_captcha'])) {
            $sanitized['enable_captcha'] = (bool) $input['enable_captcha'];
        }

        if (isset($input['captcha_provider'])) {
            $allowed_providers = array('recaptcha', 'hcaptcha');
            $sanitized['captcha_provider'] = in_array($input['captcha_provider'], $allowed_providers) ? $input['captcha_provider'] : 'recaptcha';
        }

        if (isset($input['captcha_site_key'])) {
            $sanitized['captcha_site_key'] = sanitize_text_field($input['captcha_site_key']);
        }

        if (isset($input['captcha_secret_key'])) {
            $sanitized['captcha_secret_key'] = sanitize_text_field($input['captcha_secret_key']);
        }

        $security_options = array('enable_security_headers', 'enable_csrf_protection', 'enable_https_only');
        foreach ($security_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        return $sanitized;
    }

    /**
     * AJAX handler for testing Firebase connection
     */
    public function ajax_test_firebase() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Get Firebase handler
        $firebase_auth = new Smart_Auth_Firebase();

        // Test connection
        $result = $firebase_auth->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message(),
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Firebase connection successful!', 'smart-auth'),
            ));
        }
    }

    /**
     * AJAX handler for testing Twilio connection
     */
    public function ajax_test_twilio() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Get Twilio handler
        $twilio_auth = new Smart_Auth_Twilio();

        // Test connection
        $result = $twilio_auth->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message(),
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Twilio connection successful!', 'smart-auth'),
            ));
        }
    }

    /**
     * AJAX handler for generating JWT secret
     */
    public function ajax_generate_jwt_secret() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Generate new secret
        $secret = wp_generate_password(64, true, true);

        wp_send_json_success(array(
            'secret' => $secret,
            'message' => __('New JWT secret generated successfully!', 'smart-auth'),
        ));
    }
}
