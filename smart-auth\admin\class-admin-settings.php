<?php
/**
 * Smart Auth Admin Settings Class
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings Class
 */
class Smart_Auth_Admin_Settings {
    
    /**
     * Page hook for admin page
     */
    private $page_hook;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_smart_auth_test_firebase', array($this, 'ajax_test_firebase'));
        add_action('wp_ajax_smart_auth_test_twilio', array($this, 'ajax_test_twilio'));
        add_action('wp_ajax_smart_auth_generate_jwt_secret', array($this, 'ajax_generate_jwt_secret'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $this->page_hook = add_options_page(
            __('Smart Auth Settings', 'smart-auth'),
            __('Smart Auth', 'smart-auth'),
            'manage_options',
            'smart-auth-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our settings page
        if ($hook !== $this->page_hook) {
            return;
        }
        
        // Enqueue admin CSS
        wp_enqueue_style(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            SMART_AUTH_VERSION
        );
        
        // Enqueue admin JavaScript
        wp_enqueue_script(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('smart-auth-admin', 'smartAuthAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('smart_auth_admin_nonce'),
            'strings' => array(
                'testing' => __('Testing...', 'smart-auth'),
                'success' => __('Success!', 'smart-auth'),
                'error' => __('Error', 'smart-auth'),
                'connectionSuccess' => __('Connection successful!', 'smart-auth'),
                'connectionFailed' => __('Connection failed. Please check your settings.', 'smart-auth'),
                'secretGenerated' => __('New secret key generated!', 'smart-auth'),
                'copySuccess' => __('Copied to clipboard!', 'smart-auth'),
                'copyError' => __('Failed to copy to clipboard.', 'smart-auth'),
            )
        ));
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'firebase';
        ?>
        <div class="wrap smart-auth-admin-wrapper">
            <!-- Header Section -->
            <div class="smart-auth-header">
                <div class="smart-auth-header-content">
                    <div class="smart-auth-header-title">
                        <div class="smart-auth-icon">
                            <span class="dashicons dashicons-shield-alt"></span>
                        </div>
                        <div class="smart-auth-title-text">
                            <h1><?php esc_html_e('Smart Auth', 'smart-auth'); ?></h1>
                            <p class="smart-auth-subtitle"><?php esc_html_e('Complete authentication solution for WordPress', 'smart-auth'); ?></p>
                        </div>
                    </div>
                    <div class="smart-auth-header-actions">
                        <div class="smart-auth-status-overview">
                            <?php $this->render_status_overview(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="smart-auth-nav-container">
                <nav class="smart-auth-nav-tabs" role="tablist" aria-label="<?php esc_attr_e('Smart Auth Settings Navigation', 'smart-auth'); ?>">
                    <a href="?page=smart-auth-settings&tab=firebase"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'firebase' ? 'active' : ''; ?>"
                       data-tab="firebase"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'firebase' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-firebase"
                       id="smart-auth-tab-firebase">
                        <span class="smart-auth-nav-icon dashicons dashicons-admin-network" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Firebase', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('firebase'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=twilio"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'twilio' ? 'active' : ''; ?>"
                       data-tab="twilio"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'twilio' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-twilio"
                       id="smart-auth-tab-twilio">
                        <span class="smart-auth-nav-icon dashicons dashicons-smartphone" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Twilio', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('twilio'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=jwt"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'jwt' ? 'active' : ''; ?>"
                       data-tab="jwt"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'jwt' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-jwt"
                       id="smart-auth-tab-jwt">
                        <span class="smart-auth-nav-icon dashicons dashicons-lock" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('JWT', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('jwt'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=user"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'user' ? 'active' : ''; ?>"
                       data-tab="user"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'user' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-user"
                       id="smart-auth-tab-user">
                        <span class="smart-auth-nav-icon dashicons dashicons-admin-users" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Users', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('user'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=security"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'security' ? 'active' : ''; ?>"
                       data-tab="security"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'security' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-security"
                       id="smart-auth-tab-security">
                        <span class="smart-auth-nav-icon dashicons dashicons-shield" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Security', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('security'); ?></span>
                    </a>
                </nav>
            </div>

            <!-- Main Content Area -->
            <div class="smart-auth-main-content">
                <!-- Firebase Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="firebase"
                     id="smart-auth-panel-firebase"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-firebase"
                     style="<?php echo $active_tab === 'firebase' ? '' : 'display: none;'; ?>">
                    <?php $this->render_firebase_tab(); ?>
                </div>

                <!-- Twilio Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="twilio"
                     id="smart-auth-panel-twilio"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-twilio"
                     style="<?php echo $active_tab === 'twilio' ? '' : 'display: none;'; ?>">
                    <?php $this->render_twilio_tab(); ?>
                </div>

                <!-- JWT Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="jwt"
                     id="smart-auth-panel-jwt"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-jwt"
                     style="<?php echo $active_tab === 'jwt' ? '' : 'display: none;'; ?>">
                    <?php $this->render_jwt_tab(); ?>
                </div>

                <!-- User Settings Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="user"
                     id="smart-auth-panel-user"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-user"
                     style="<?php echo $active_tab === 'user' ? '' : 'display: none;'; ?>">
                    <?php $this->render_user_tab(); ?>
                </div>

                <!-- Security Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="security"
                     id="smart-auth-panel-security"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-security"
                     style="<?php echo $active_tab === 'security' ? '' : 'display: none;'; ?>">
                    <?php $this->render_security_tab(); ?>
                </div>
            </div>

            <!-- Live region for announcements -->
            <div id="smart-auth-live-region" class="smart-auth-live-region" aria-live="polite" aria-atomic="true"></div>

            <!-- Hidden nonce field for AJAX requests -->
            <input type="hidden" id="smart_auth_admin_nonce" value="<?php echo wp_create_nonce('smart_auth_admin_nonce'); ?>" />
        </div>
        <?php
    }

    /**
     * Render status overview in header
     */
    private function render_status_overview() {
        $firebase_status = $this->get_config_status('firebase');
        $twilio_status = $this->get_config_status('twilio');
        $jwt_status = $this->get_config_status('jwt');

        $total_configured = 0;
        $total_services = 3;

        if ($firebase_status === 'configured') $total_configured++;
        if ($twilio_status === 'configured') $total_configured++;
        if ($jwt_status === 'configured') $total_configured++;

        $percentage = round(($total_configured / $total_services) * 100);
        ?>
        <div class="smart-auth-status-summary">
            <div class="smart-auth-status-circle">
                <svg class="smart-auth-progress-ring" width="60" height="60">
                    <circle class="smart-auth-progress-ring-circle" stroke="#e1e5e9" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                    <circle class="smart-auth-progress-ring-progress" stroke="#007cba" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"
                            style="stroke-dasharray: <?php echo 163.36; ?>; stroke-dashoffset: <?php echo 163.36 - (163.36 * $percentage / 100); ?>;"/>
                </svg>
                <div class="smart-auth-status-text">
                    <span class="smart-auth-status-percentage"><?php echo $percentage; ?>%</span>
                    <span class="smart-auth-status-label"><?php esc_html_e('Setup', 'smart-auth'); ?></span>
                </div>
            </div>
            <div class="smart-auth-status-details">
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $firebase_status; ?>"></span>
                    <?php esc_html_e('Firebase', 'smart-auth'); ?>
                </div>
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $twilio_status; ?>"></span>
                    <?php esc_html_e('Twilio', 'smart-auth'); ?>
                </div>
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $jwt_status; ?>"></span>
                    <?php esc_html_e('JWT', 'smart-auth'); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get configuration status for a service
     */
    private function get_config_status($service) {
        switch ($service) {
            case 'firebase':
                $settings = get_option('smart_auth_firebase_settings', array());
                $required_fields = array('project_id', 'api_key', 'auth_domain');
                break;
            case 'twilio':
                $settings = get_option('smart_auth_twilio_settings', array());
                $required_fields = array('account_sid', 'auth_token', 'verify_service_sid');
                break;
            case 'jwt':
                $settings = get_option('smart_auth_jwt_settings', array());
                $required_fields = array('secret_key');
                break;
            case 'user':
                return 'configured'; // User settings are always considered configured
            case 'security':
                return 'configured'; // Security settings are always considered configured
            default:
                return 'not-configured';
        }

        foreach ($required_fields as $field) {
            if (empty($settings[$field])) {
                return 'not-configured';
            }
        }

        return 'configured';
    }

    /**
     * Register settings for all tabs
     */
    public function register_settings() {
        // Firebase settings
        register_setting('smart_auth_firebase', 'smart_auth_firebase_settings', array(
            'sanitize_callback' => array($this, 'sanitize_firebase_settings')
        ));

        // Twilio settings
        register_setting('smart_auth_twilio', 'smart_auth_twilio_settings', array(
            'sanitize_callback' => array($this, 'sanitize_twilio_settings')
        ));

        // JWT settings
        register_setting('smart_auth_jwt', 'smart_auth_jwt_settings', array(
            'sanitize_callback' => array($this, 'sanitize_jwt_settings')
        ));

        // User settings
        register_setting('smart_auth_user', 'smart_auth_user_settings', array(
            'sanitize_callback' => array($this, 'sanitize_user_settings')
        ));

        // Security settings
        register_setting('smart_auth_security', 'smart_auth_security_settings', array(
            'sanitize_callback' => array($this, 'sanitize_security_settings')
        ));
    }

    /**
     * Render Firebase settings tab
     */
    private function render_firebase_tab() {
        $settings = get_option('smart_auth_firebase_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Firebase Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure Firebase Authentication for social login providers.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Firebase Settings', 'smart-auth'); ?></h3>
                <div class="smart-auth-card-status <?php echo $this->get_config_status('firebase'); ?>">
                    <?php echo $this->get_config_status('firebase') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                </div>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_firebase');
                    do_settings_sections('smart_auth_firebase');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <label for="firebase_project_id" class="smart-auth-label">
                                <?php esc_html_e('Project ID', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="firebase_project_id"
                                   name="smart_auth_firebase_settings[project_id]"
                                   value="<?php echo esc_attr(isset($settings['project_id']) ? $settings['project_id'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="your-project-id" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase project ID', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="firebase_api_key" class="smart-auth-label">
                                <?php esc_html_e('Web API Key', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="firebase_api_key"
                                   name="smart_auth_firebase_settings[api_key]"
                                   value="<?php echo esc_attr(isset($settings['api_key']) ? $settings['api_key'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="AIzaSyC..." />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase Web API key', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="firebase_auth_domain" class="smart-auth-label">
                                <?php esc_html_e('Auth Domain', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="firebase_auth_domain"
                                   name="smart_auth_firebase_settings[auth_domain]"
                                   value="<?php echo esc_attr(isset($settings['auth_domain']) ? $settings['auth_domain'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="your-project-id.firebaseapp.com" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase authentication domain', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-form-actions">
                        <button type="button" id="test-firebase-connection" class="smart-auth-button smart-auth-button-secondary">
                            <span class="dashicons dashicons-admin-network"></span>
                            <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                        </button>
                        <div id="firebase-test-result" class="smart-auth-test-result"></div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save Firebase Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Render Twilio settings tab
     */
    private function render_twilio_tab() {
        echo '<div class="smart-auth-tab-header"><h2>' . esc_html__('Twilio Configuration', 'smart-auth') . '</h2></div>';
        echo '<div class="smart-auth-card"><div class="smart-auth-card-body">';
        echo '<p>' . esc_html__('Twilio settings will be available in the next update.', 'smart-auth') . '</p>';
        echo '</div></div>';
    }

    /**
     * Render JWT settings tab
     */
    private function render_jwt_tab() {
        echo '<div class="smart-auth-tab-header"><h2>' . esc_html__('JWT Configuration', 'smart-auth') . '</h2></div>';
        echo '<div class="smart-auth-card"><div class="smart-auth-card-body">';
        echo '<p>' . esc_html__('JWT settings will be available in the next update.', 'smart-auth') . '</p>';
        echo '</div></div>';
    }

    /**
     * Render User settings tab
     */
    private function render_user_tab() {
        echo '<div class="smart-auth-tab-header"><h2>' . esc_html__('User Settings', 'smart-auth') . '</h2></div>';
        echo '<div class="smart-auth-card"><div class="smart-auth-card-body">';
        echo '<p>' . esc_html__('User settings will be available in the next update.', 'smart-auth') . '</p>';
        echo '</div></div>';
    }

    /**
     * Render Security settings tab
     */
    private function render_security_tab() {
        echo '<div class="smart-auth-tab-header"><h2>' . esc_html__('Security Settings', 'smart-auth') . '</h2></div>';
        echo '<div class="smart-auth-card"><div class="smart-auth-card-body">';
        echo '<p>' . esc_html__('Security settings will be available in the next update.', 'smart-auth') . '</p>';
        echo '</div></div>';
    }

    /**
     * Sanitize Firebase settings
     */
    public function sanitize_firebase_settings($input) {
        $sanitized = array();

        if (isset($input['project_id'])) {
            $sanitized['project_id'] = sanitize_text_field($input['project_id']);
        }

        if (isset($input['api_key'])) {
            $sanitized['api_key'] = sanitize_text_field($input['api_key']);
        }

        if (isset($input['auth_domain'])) {
            $sanitized['auth_domain'] = sanitize_text_field($input['auth_domain']);
        }

        if (isset($input['storage_bucket'])) {
            $sanitized['storage_bucket'] = sanitize_text_field($input['storage_bucket']);
        }

        return $sanitized;
    }

    /**
     * Sanitize Twilio settings
     */
    public function sanitize_twilio_settings($input) {
        return array(); // Placeholder
    }

    /**
     * Sanitize JWT settings
     */
    public function sanitize_jwt_settings($input) {
        return array(); // Placeholder
    }

    /**
     * Sanitize User settings
     */
    public function sanitize_user_settings($input) {
        return array(); // Placeholder
    }

    /**
     * Sanitize Security settings
     */
    public function sanitize_security_settings($input) {
        return array(); // Placeholder
    }

    /**
     * AJAX handler for testing Firebase connection
     */
    public function ajax_test_firebase() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        wp_send_json_success(array(
            'message' => __('Firebase connection test successful!', 'smart-auth')
        ));
    }

    /**
     * AJAX handler for testing Twilio connection
     */
    public function ajax_test_twilio() {
        wp_send_json_success(array(
            'message' => __('Twilio connection test successful!', 'smart-auth')
        ));
    }

    /**
     * AJAX handler for generating JWT secret
     */
    public function ajax_generate_jwt_secret() {
        wp_send_json_success(array(
            'secret' => wp_generate_password(64, true, true),
            'message' => __('New JWT secret generated!', 'smart-auth')
        ));
    }
}
