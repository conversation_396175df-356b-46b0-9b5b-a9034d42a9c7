<?php
/**
 * Elementor Social Login Widget
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Social Login Buttons Widget for Elementor
 */
class Smart_Auth_Elementor_Social_Login_Widget extends \Elementor\Widget_Base {
    
    /**
     * Get widget name
     *
     * @return string Widget name
     */
    public function get_name() {
        return 'smart-auth-social-login';
    }
    
    /**
     * Get widget title
     *
     * @return string Widget title
     */
    public function get_title() {
        return __('Social Login Buttons', 'smart-auth');
    }
    
    /**
     * Get widget icon
     *
     * @return string Widget icon
     */
    public function get_icon() {
        return 'eicon-social-icons';
    }
    
    /**
     * Get widget categories
     *
     * @return array Widget categories
     */
    public function get_categories() {
        return array('smart-auth');
    }
    
    /**
     * Get widget keywords
     *
     * @return array Widget keywords
     */
    public function get_keywords() {
        return array('social', 'login', 'google', 'facebook', 'apple', 'authentication');
    }
    
    /**
     * Register widget controls
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            array(
                'label' => __('Content', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'widget_title',
            array(
                'label' => __('Widget Title', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Sign in with', 'smart-auth'),
                'placeholder' => __('Enter widget title', 'smart-auth'),
            )
        );
        
        $this->add_control(
            'show_title',
            array(
                'label' => __('Show Title', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'smart-auth'),
                'label_off' => __('Hide', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );
        
        $this->end_controls_section();
        
        // Providers Section
        $this->start_controls_section(
            'providers_section',
            array(
                'label' => __('Providers', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'enable_google',
            array(
                'label' => __('Enable Google', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'smart-auth'),
                'label_off' => __('No', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );
        
        $this->add_control(
            'google_text',
            array(
                'label' => __('Google Button Text', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Continue with Google', 'smart-auth'),
                'condition' => array(
                    'enable_google' => 'yes',
                ),
            )
        );
        
        $this->add_control(
            'enable_facebook',
            array(
                'label' => __('Enable Facebook', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'smart-auth'),
                'label_off' => __('No', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );
        
        $this->add_control(
            'facebook_text',
            array(
                'label' => __('Facebook Button Text', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Continue with Facebook', 'smart-auth'),
                'condition' => array(
                    'enable_facebook' => 'yes',
                ),
            )
        );
        
        $this->add_control(
            'enable_apple',
            array(
                'label' => __('Enable Apple', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'smart-auth'),
                'label_off' => __('No', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'no',
            )
        );
        
        $this->add_control(
            'apple_text',
            array(
                'label' => __('Apple Button Text', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Continue with Apple', 'smart-auth'),
                'condition' => array(
                    'enable_apple' => 'yes',
                ),
            )
        );
        
        $this->end_controls_section();
        
        // Settings Section
        $this->start_controls_section(
            'settings_section',
            array(
                'label' => __('Settings', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'button_layout',
            array(
                'label' => __('Button Layout', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => array(
                    'vertical' => __('Vertical', 'smart-auth'),
                    'horizontal' => __('Horizontal', 'smart-auth'),
                    'grid' => __('Grid', 'smart-auth'),
                ),
                'default' => 'vertical',
            )
        );
        
        $this->add_control(
            'redirect_url',
            array(
                'label' => __('Redirect URL', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::URL,
                'placeholder' => __('https://your-domain.com', 'smart-auth'),
                'show_external' => true,
                'default' => array(
                    'url' => '',
                    'is_external' => false,
                    'nofollow' => false,
                ),
            )
        );
        
        $this->add_control(
            'show_icons',
            array(
                'label' => __('Show Icons', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'smart-auth'),
                'label_off' => __('Hide', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );
        
        $this->end_controls_section();
        
        // Title Style Section
        $this->start_controls_section(
            'title_style_section',
            array(
                'label' => __('Title', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => array(
                    'show_title' => 'yes',
                ),
            )
        );

        $this->add_control(
            'title_color',
            array(
                'label' => __('Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-social-title' => 'color: {{VALUE}}',
                ),
            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'title_typography',
                'selector' => '{{WRAPPER}} .smart-auth-social-title',
            )
        );

        $this->add_responsive_control(
            'title_margin',
            array(
                'label' => __('Margin', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => array('px', 'em', '%'),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-social-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );

        $this->add_responsive_control(
            'title_align',
            array(
                'label' => __('Alignment', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => array(
                    'left' => array(
                        'title' => __('Left', 'smart-auth'),
                        'icon' => 'eicon-text-align-left',
                    ),
                    'center' => array(
                        'title' => __('Center', 'smart-auth'),
                        'icon' => 'eicon-text-align-center',
                    ),
                    'right' => array(
                        'title' => __('Right', 'smart-auth'),
                        'icon' => 'eicon-text-align-right',
                    ),
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-social-title' => 'text-align: {{VALUE}};',
                ),
            )
        );

        $this->end_controls_section();

        // Button Style Section
        $this->start_controls_section(
            'button_style_section',
            array(
                'label' => __('Buttons', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_responsive_control(
            'button_width',
            array(
                'label' => __('Width', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => array('px', '%', 'vw'),
                'range' => array(
                    'px' => array(
                        'min' => 100,
                        'max' => 800,
                    ),
                    '%' => array(
                        'min' => 10,
                        'max' => 100,
                    ),
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'width: {{SIZE}}{{UNIT}};',
                ),
            )
        );

        $this->add_responsive_control(
            'button_height',
            array(
                'label' => __('Height', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => array('px', 'em'),
                'range' => array(
                    'px' => array(
                        'min' => 30,
                        'max' => 100,
                    ),
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'height: {{SIZE}}{{UNIT}};',
                ),
            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'button_typography',
                'selector' => '{{WRAPPER}} .smart-auth-button',
            )
        );

        $this->start_controls_tabs('button_style_tabs');

        // Normal State
        $this->start_controls_tab(
            'button_normal_tab',
            array(
                'label' => __('Normal', 'smart-auth'),
            )
        );

        $this->add_control(
            'button_text_color',
            array(
                'label' => __('Text Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'color: {{VALUE}};',
                ),
            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Background::get_type(),
            array(
                'name' => 'button_background',
                'selector' => '{{WRAPPER}} .smart-auth-button',
            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            array(
                'name' => 'button_box_shadow',
                'selector' => '{{WRAPPER}} .smart-auth-button',
            )
        );

        $this->end_controls_tab();

        // Hover State
        $this->start_controls_tab(
            'button_hover_tab',
            array(
                'label' => __('Hover', 'smart-auth'),
            )
        );

        $this->add_control(
            'button_hover_text_color',
            array(
                'label' => __('Text Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button:hover' => 'color: {{VALUE}};',
                ),
            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Background::get_type(),
            array(
                'name' => 'button_hover_background',
                'selector' => '{{WRAPPER}} .smart-auth-button:hover',
            )
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            array(
                'name' => 'button_hover_box_shadow',
                'selector' => '{{WRAPPER}} .smart-auth-button:hover',
            )
        );

        $this->add_control(
            'button_hover_transition',
            array(
                'label' => __('Transition Duration', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'range' => array(
                    'px' => array(
                        'max' => 3,
                        'step' => 0.1,
                    ),
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'transition: all {{SIZE}}s ease;',
                ),
            )
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            array(
                'name' => 'button_border',
                'selector' => '{{WRAPPER}} .smart-auth-button',
                'separator' => 'before',
            )
        );

        $this->add_responsive_control(
            'button_border_radius',
            array(
                'label' => __('Border Radius', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => array('px', '%'),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );

        $this->add_responsive_control(
            'button_padding',
            array(
                'label' => __('Padding', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => array('px', 'em', '%'),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );

        $this->add_responsive_control(
            'button_margin',
            array(
                'label' => __('Margin', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => array('px', 'em', '%'),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );

        $this->end_controls_section();

        // Layout Style Section
        $this->start_controls_section(
            'layout_style_section',
            array(
                'label' => __('Layout', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            )
        );

        $this->add_responsive_control(
            'container_align',
            array(
                'label' => __('Alignment', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => array(
                    'left' => array(
                        'title' => __('Left', 'smart-auth'),
                        'icon' => 'eicon-text-align-left',
                    ),
                    'center' => array(
                        'title' => __('Center', 'smart-auth'),
                        'icon' => 'eicon-text-align-center',
                    ),
                    'right' => array(
                        'title' => __('Right', 'smart-auth'),
                        'icon' => 'eicon-text-align-right',
                    ),
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-social-buttons' => 'text-align: {{VALUE}};',
                ),
            )
        );

        $this->add_responsive_control(
            'button_spacing',
            array(
                'label' => __('Button Spacing', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => array('px', 'em'),
                'range' => array(
                    'px' => array(
                        'min' => 0,
                        'max' => 50,
                    ),
                ),
                'default' => array(
                    'unit' => 'px',
                    'size' => 12,
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-social-buttons.layout-vertical .smart-auth-button:not(:last-child)' => 'margin-bottom: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .smart-auth-social-buttons.layout-horizontal .smart-auth-button:not(:last-child)' => 'margin-right: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .smart-auth-social-buttons.layout-grid' => 'gap: {{SIZE}}{{UNIT}};',
                ),
            )
        );

        $this->add_responsive_control(
            'container_padding',
            array(
                'label' => __('Container Padding', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => array('px', 'em', '%'),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-social-login-widget' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );

        $this->end_controls_section();
    }
    
    /**
     * Render widget output on the frontend
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $widget_title = !empty($settings['widget_title']) ? $settings['widget_title'] : '';
        $show_title = $settings['show_title'] === 'yes';
        $button_layout = !empty($settings['button_layout']) ? $settings['button_layout'] : 'vertical';
        $redirect_url = !empty($settings['redirect_url']['url']) ? $settings['redirect_url']['url'] : '';
        $show_icons = $settings['show_icons'] === 'yes';
        
        // Build enabled providers array
        $enabled_providers = array();
        if ($settings['enable_google'] === 'yes') {
            $enabled_providers['google'] = !empty($settings['google_text']) ? $settings['google_text'] : __('Continue with Google', 'smart-auth');
        }
        if ($settings['enable_facebook'] === 'yes') {
            $enabled_providers['facebook'] = !empty($settings['facebook_text']) ? $settings['facebook_text'] : __('Continue with Facebook', 'smart-auth');
        }
        if ($settings['enable_apple'] === 'yes') {
            $enabled_providers['apple'] = !empty($settings['apple_text']) ? $settings['apple_text'] : __('Continue with Apple', 'smart-auth');
        }
        
        if (empty($enabled_providers)) {
            echo '<p>' . esc_html__('No social login providers enabled.', 'smart-auth') . '</p>';
            return;
        }
        
        ?>
        <div class="smart-auth-social-login-widget">
            <?php if ($show_title && !empty($widget_title)) : ?>
                <h3 class="smart-auth-social-title"><?php echo esc_html($widget_title); ?></h3>
            <?php endif; ?>
            
            <div class="smart-auth-social-buttons layout-<?php echo esc_attr($button_layout); ?>" data-redirect-url="<?php echo esc_attr($redirect_url); ?>">
                <?php foreach ($enabled_providers as $provider => $text) : ?>
                    <button type="button" class="smart-auth-button smart-auth-<?php echo esc_attr($provider); ?>-button" data-provider="<?php echo esc_attr($provider); ?>">
                        <?php if ($show_icons) : ?>
                            <?php $this->render_provider_icon($provider); ?>
                        <?php endif; ?>
                        <span class="button-text"><?php echo esc_html($text); ?></span>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render provider icon
     *
     * @param string $provider Provider name
     */
    private function render_provider_icon($provider) {
        $icons = array(
            'google' => '<svg width="18" height="18" viewBox="0 0 18 18"><path fill="#4285F4" d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18z"/><path fill="#34A853" d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2.04a4.8 4.8 0 0 1-7.18-2.53H1.83v2.07A8 8 0 0 0 8.98 17z"/><path fill="#FBBC05" d="M4.5 10.49a4.8 4.8 0 0 1 0-3.07V5.35H1.83a8 8 0 0 0 0 7.28l2.67-2.14z"/><path fill="#EA4335" d="M8.98 4.72c1.16 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.35L4.5 7.42a4.77 4.77 0 0 1 4.48-2.7z"/></svg>',
            'facebook' => '<svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>',
            'apple' => '<svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02 1.05.02 1.05C8.045 3.65 9.792 5.474 12.017 5.474s3.972-1.824 3.972-4.38c0 0-.03-.583-.03-1.05C15.959.044 15.588 0 12.017 0zm7.624 9.409c-.877-1.426-2.08-2.141-3.467-2.141-1.235 0-2.185.728-3.467.728-1.282 0-2.232-.728-3.467-.728-1.387 0-2.59.715-3.467 2.141C4.895 10.835 4.5 12.5 4.5 14.5c0 4.5 2.5 9.5 5.5 9.5 1.5 0 2.5-1 3.5-1s2 1 3.5 1c3 0 5.5-5 5.5-9.5 0-2-0.395-3.665-1.359-5.091z"/></svg>',
        );
        
        if (isset($icons[$provider])) {
            echo $icons[$provider];
        }
    }
    
    /**
     * Render widget output in the editor
     */
    protected function content_template() {
        ?>
        <#
        var widgetTitle = settings.widget_title || '<?php echo esc_js(__('Sign in with', 'smart-auth')); ?>';
        var showTitle = settings.show_title === 'yes';
        var buttonLayout = settings.button_layout || 'vertical';
        var showIcons = settings.show_icons === 'yes';
        
        var enabledProviders = {};
        if (settings.enable_google === 'yes') {
            enabledProviders.google = settings.google_text || '<?php echo esc_js(__('Continue with Google', 'smart-auth')); ?>';
        }
        if (settings.enable_facebook === 'yes') {
            enabledProviders.facebook = settings.facebook_text || '<?php echo esc_js(__('Continue with Facebook', 'smart-auth')); ?>';
        }
        if (settings.enable_apple === 'yes') {
            enabledProviders.apple = settings.apple_text || '<?php echo esc_js(__('Continue with Apple', 'smart-auth')); ?>';
        }
        #>
        
        <div class="smart-auth-social-login-widget">
            <# if (showTitle && widgetTitle) { #>
                <h3 class="smart-auth-social-title">{{{ widgetTitle }}}</h3>
            <# } #>
            
            <div class="smart-auth-social-buttons layout-{{ buttonLayout }}">
                <# _.each(enabledProviders, function(text, provider) { #>
                    <button type="button" class="smart-auth-button smart-auth-{{ provider }}-button">
                        <# if (showIcons) { #>
                            <span class="button-icon">🔗</span>
                        <# } #>
                        <span class="button-text">{{{ text }}}</span>
                    </button>
                <# }); #>
            </div>
            
            <# if (_.isEmpty(enabledProviders)) { #>
                <p><?php esc_html_e('No social login providers enabled.', 'smart-auth'); ?></p>
            <# } #>
        </div>
        <?php
    }
}
