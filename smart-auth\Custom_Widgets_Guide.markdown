# Creating Fully Custom Widgets in Elementor and Bricks Builder

This guide provides a comprehensive, step-by-step process for programmers to create fully custom widgets in Elementor and Bricks Builder, two popular WordPress page builders. The instructions are based on official documentation and include code examples to ensure clarity and practical application. By following these steps, developers can extend the functionality of these builders to meet specific project requirements.

## Elementor

Elementor is a leading WordPress page builder that allows developers to create custom widgets to enhance its functionality. The process involves creating a WordPress plugin, defining widget classes, and registering them with Elementor’s widget manager. Below are the detailed steps to create a custom widget, along with code examples.

### Steps to Create a Custom Widget in Elementor

1. **Create Folder Structure:**
   - Create a new folder in the `wp-content/plugins/` directory of your WordPress installation, e.g., `elementor-addon`.
   - Inside this folder, create a subdirectory named `widgets` to store your widget files.

2. **Main Addon File:**
   - Create a main PHP file named `elementor-addon.php` in the root of your addon folder. This file includes the plugin header and registers your widgets using the `elementor/widgets/register` hook.
   - Example content for `elementor-addon.php`:
     ```php
     <?php
     /**
      * Plugin Name: Elementor Addon
      * Description: Simple hello world widgets for Elementor
      * Version: 1.0.0
      * Author: Your Name
      * Author URI: https://yourwebsite.com
      * Text Domain: elementor-addon
      */

     add_action( 'elementor/widgets/register', 'register_hello_world_widget' );

     function register_hello_world_widget( $widgets_manager ) {
         require_once( __DIR__ . '/widgets/hello-world-widget-1.php' );
         require_once( __DIR__ . '/widgets/hello-world-widget-2.php' );
         $widgets_manager->register( new \Hello_World_Widget_1() );
         $widgets_manager->register( new \Hello_World_Widget_2() );
     }
     ```

3. **Widget Files:**
   - For each custom widget, create a separate PHP file in the `widgets/` directory, e.g., `hello-world-widget-1.php` for a simple widget and `hello-world-widget-2.php` for a more complex one.
   - Each widget file should extend the `\Elementor\Widget_Base` class and implement key methods:
     - `get_name()`: Returns a unique identifier for the widget.
     - `get_title()`: Returns the display name shown in the Elementor panel.
     - `get_icon()`: Specifies the icon for the widget in the panel.
     - `get_categories()`: Defines the category under which the widget appears.
     - `get_keywords()`: Provides keywords for searching the widget.
     - `register_controls()`: Defines the controls (input fields) for user customization.
     - `render()`: Outputs the widget’s HTML on the frontend.
     - `content_template()`: Defines the template for the widget’s content (optional for simple widgets).

   - **Example: Simple Widget (`hello-world-widget-1.php`):**
     ```php
     <?php
     class Hello_World_Widget_1 extends \Elementor\Widget_Base {
         public function get_name() {
             return 'hello_world_widget_1';
         }

         public function get_title() {
             return __( 'Hello World 1', 'elementor-addon' );
         }

         public function get_icon() {
             return 'eicon-code';
         }

         public function get_categories() {
             return [ 'basic' ];
         }

         protected function render() {
             echo 'Hello World';
         }
     }
     ```

   - **Example: Complex Widget with Controls (`hello-world-widget-2.php`):**
     ```php
     <?php
     class Hello_World_Widget_2 extends \Elementor\Widget_Base {
         public function get_name() {
             return 'hello_world_widget_2';
         }

         public function get_title() {
             return __( 'Hello World 2', 'elementor-addon' );
         }

         public function get_icon() {
             return 'eicon-code';
         }

         public function get_categories() {
             return [ 'basic' ];
         }

         protected function register_controls() {
             $this->start_controls_section(
                 'content_section',
                 [
                     'label' => __( 'Content', 'elementor-addon' ),
                     'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
                 ]
             );

             $this->add_control(
                 'title',
                 [
                     'label' => __( 'Title', 'elementor-addon' ),
                     'type' => \Elementor\Controls_Manager::TEXTAREA,
                     'default' => 'Hello world',
                 ]
             );

             $this->end_controls_section();

             $this->start_controls_section(
                 'style_section',
                 [
                     'label' => __( 'Style', 'elementor-addon' ),
                     'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                 ]
             );

             $this->add_control(
                 'color',
                 [
                     'label' => __( 'Color', 'elementor-addon' ),
                     'type' => \Elementor\Controls_Manager::COLOR,
                     'default' => '#000000',
                 ]
             );

             $this->end_controls_section();
         }

         protected function render() {
             $settings = $this->get_settings_for_display();
             echo '<div class="hello-world" style="color: ' . $settings['color'] . ';">' . $settings['title'] . '</div>';
         }
     }
     ```

4. **Upload and Activate:**
   - Zip the entire `elementor-addon` folder.
   - Upload the zip file via the WordPress dashboard under "Plugins" > "Add New."
   - Activate the plugin to make your widgets available in Elementor.

### Additional Notes for Elementor
- **Controls**: Elementor provides a variety of controls (e.g., text, color, select) to create interactive widgets. Refer to the Elementor Developers Documentation for a full list of control types.
- **Testing**: Test your widgets in a staging environment to ensure compatibility with Elementor and other plugins.
- **Best Practices**: Follow WordPress coding standards, use object-oriented programming, and ensure your plugin checks for Elementor’s presence before loading.

### Citations for Elementor
| Source | URL |
|--------|-----|
| Elementor Developers Documentation | [https://developers.elementor.com/docs/widgets/](https://developers.elementor.com/docs/widgets/) |
| Creating Your First Addon | [https://developers.elementor.com/docs/getting-started/first-addon/](https://developers.elementor.com/docs/getting-started/first-addon/) |
| Simple Example | [https://developers.elementor.com/docs/widgets/simple-example/](https://developers.elementor.com/docs/widgets/simple-example/) |

## Bricks Builder

Bricks Builder is a modern WordPress page builder that supports custom element creation within a child theme. Custom elements (referred to as widgets in other contexts) are defined by extending the `\Bricks\Element` class and registering them in the theme’s functions file. The following steps outline the process, with a code example from the Bricks Academy.

### Steps to Create a Custom Element in Bricks Builder

1. **Create a PHP File:**
   - In the raiz folder of your Bricks child theme, create a new PHP file, e.g., `element-test.php`.

2. **Extend the Element Class:**
   - Define a class in `element-test.php` that extends `\Bricks\Element`. Set the following properties:
     - `$category`: The category for the element (e.g., 'general').
     - `$name`: A unique name (e.g., 'prefix-test').
     - `$icon`: The icon displayed in the builder (e.g., 'ti-bolt-alt').
     - `$css_selector`: The CSS selector for styling (e.g., '.prefix-test-wrapper').
     - `$scripts`: An array of scripts to enqueue (e.g., ['prefixElementTest']).
     - `$nestable`: A boolean indicating if the element can be nested (true or false).
   - Implement methods:
     - `get_label()`: Returns the element’s label.
     - `get_keywords()`: Returns search keywords.
     - `set_control_groups()`: Defines control groups for settings.
     - `set_controls()`: Defines individual controls (e.g., text, select).
     - `enqueue_scripts()`: Enqueues necessary scripts.
     - `render()`: Outputs the element’s HTML.

   - **Example: `element-test.php`:**
     ```php
     <?php
     if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

     class Prefix_Element_Test extends \Bricks\Element {
         public $category = 'general';
         public $name = 'prefix-test';
         public $icon = 'ti-bolt-alt';
         public $css_selector = '.prefix-test-wrapper';
         public $scripts = ['prefixElementTest'];
         public $nestable = false;

         public function get_label() {
             return __( 'Test Element', 'prefix' );
         }

         public function get_keywords() {
             return [ 'test', 'element' ];
         }

         public function set_control_groups() {
             $this->control_groups['text_group'] = [
                 'title' => __( 'Text', 'prefix' ),
                 'tab' => 'content',
             ];

             $this->control_groups['settings_group'] = [
                 'title' => __( 'Settings', 'prefix' ),
                 'tab' => 'content',
             ];
         }

         public function set_controls() {
             $this->controls['content'] = [
                 'tab' => 'content',
                 'group' => 'text_group',
                 'label' => __( 'Content', 'prefix' ),
                 'type' => 'text',
             ];

             $this->controls['type'] = [
                 'tab' => 'content',
                 'group' => 'settings_group',
                 'label' => __( 'Type', 'prefix' ),
                 'type' => 'select',
                 'options' => [
                     'info' => __( 'Info', 'prefix' ),
                     'success' => __( 'Success', 'prefix' ),
                     'warning' => __( 'Warning', 'prefix' ),
                     'danger' => __( 'Danger', 'prefix' ),
                     'muted' => __( 'Muted', 'prefix' ),
                 ],
             ];
         }

         public function enqueue_scripts() {
             wp_enqueue_script( 'prefix-test-script', get_stylesheet_directory_uri() . '/js/element-test.js', [], false, true );
         }

         public function render() {
             $settings = $this->settings;

             $this->set_attribute( 'wrapper', 'class', 'prefix-test-wrapper' );
             $this->set_attribute( 'wrapper', 'class', 'prefix-test-' . ( isset( $settings['type'] ) ? $settings['type'] : 'info' ) );

             echo "<div {$this->render_attributes( 'wrapper' )}>";
             echo '<p>' . esc_html( isset( $settings['content'] ) ? $settings['content'] : '' ) . '</p>';
             echo '</div>';
         }
     }
     ```

3. **Register the Element:**
   - In your theme’s `functions.php` file, add the following code to register the custom element:
     ```php
     add_action( 'init', function() {
         \Bricks\Elements::register_element( __DIR__ . '/element-test.php' );
     } );
     ```

4. **Create Supporting Files (Optional):**
   - If your element requires custom CSS or JavaScript, create files like `css/element-test.css` or `js/element-test.js` in your child theme and enqueue them in the `enqueue_scripts()` method.

### Additional Notes for Bricks Builder
- **Nestable Elements**: Since Bricks Builder version 1.3.0, you can create nestable elements by setting `$nestable = true`. This allows users to nest other elements within your custom element, enabling complex layouts.
- **Dynamic Data**: If your element uses dynamic data (e.g., from Advanced Custom Fields), ensure the backend can access the correct page ID, as Bricks may not automatically detect it.
- **Testing**: Test your elements in the Bricks Builder editor to verify rendering and control functionality.

### Citations for Bricks Builder
| Source | URL |
|--------|-----|
| Create Your Own Elements – Bricks Academy | [https://academy.bricksbuilder.io/article/create-your-own-elements/](https://academy.bricksbuilder.io/article/create-your-own-elements/) |

## Best Practices for Both Builders
- **Code Standards**: Adhere to WordPress coding standards, using object-oriented programming and proper sanitization/escaping for security.
- **Testing**: Test widgets in a staging environment to ensure compatibility with the latest versions of Elementor, Bricks Builder, and WordPress.
- **Documentation**: Refer to the official documentation for additional control types, methods, and advanced features like dynamic data integration.
- **Performance**: Minimize external dependencies and optimize scripts/CSS to maintain fast page load times.

## Conclusion
Creating custom widgets in Elementor and Bricks Builder empowers developers to tailor WordPress sites to specific needs. Elementor’s plugin-based approach is ideal for those familiar with WordPress plugin development, while Bricks Builder’s child theme integration offers a lightweight, modern alternative. By following the steps above and leveraging the provided code examples, developers can create robust, user-friendly widgets. Always consult the official documentation for updates and advanced techniques, and test thoroughly to ensure a seamless user experience.