/**
 * Smart Auth Bricks Builder Integration Styles
 *
 * @package SmartAuth
 * @since 1.0.0
 */

/* ==========================================================================
   Bricks Builder Compatibility
   ========================================================================== */

/* Reset Bricks default styles that might conflict */
.brxe-smart-auth,
.brxe-smart-auth *,
[data-smart-auth],
[data-smart-auth] * {
    box-sizing: border-box;
}

/* Ensure Smart Auth elements don't inherit unwanted Bricks styles */
.brxe-smart-auth .smart-auth-button,
[data-smart-auth] .smart-auth-button {
    font-family: inherit;
    line-height: 1.4;
    text-decoration: none;
    border: none;
    outline: none;
    background: none;
    margin: 0;
    padding: 0;
}

/* ==========================================================================
   Social Login Wrapper for Bricks
   ========================================================================== */

.brxe-smart-auth.smart-auth-social-login-wrapper,
[data-smart-auth="social-login"] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
}

.brxe-smart-auth.smart-auth-social-login-wrapper.layout-horizontal,
[data-smart-auth="social-login"].layout-horizontal {
    flex-direction: row;
    flex-wrap: wrap;
}

.brxe-smart-auth .smart-auth-button,
[data-smart-auth] .smart-auth-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: 2px solid transparent;
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.brxe-smart-auth .smart-auth-button:hover,
[data-smart-auth] .smart-auth-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.brxe-smart-auth .smart-auth-button:active,
[data-smart-auth] .smart-auth-button:active {
    transform: translateY(0);
}

.brxe-smart-auth .smart-auth-button:disabled,
[data-smart-auth] .smart-auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Provider-specific button styles */
.smart-auth-button.google {
    background: #4285f4;
    color: white;
    border-color: #4285f4;
}

.smart-auth-button.google:hover {
    background: #3367d6;
    border-color: #3367d6;
}

.smart-auth-button.facebook {
    background: #1877f2;
    color: white;
    border-color: #1877f2;
}

.smart-auth-button.facebook:hover {
    background: #166fe5;
    border-color: #166fe5;
}

.smart-auth-button.apple {
    background: #000000;
    color: white;
    border-color: #000000;
}

.smart-auth-button.apple:hover {
    background: #333333;
    border-color: #333333;
}

.smart-auth-button.phone {
    background: #00a32a;
    color: white;
    border-color: #00a32a;
}

.smart-auth-button.phone:hover {
    background: #008a20;
    border-color: #008a20;
}

/* ==========================================================================
   Phone OTP Wrapper for Bricks
   ========================================================================== */

.brxe-smart-auth.smart-auth-phone-otp-wrapper,
[data-smart-auth="phone-otp"] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    max-width: 400px;
}

.brxe-smart-auth .smart-auth-phone-input,
.brxe-smart-auth .smart-auth-otp-input,
[data-smart-auth] .smart-auth-phone-input,
[data-smart-auth] .smart-auth-otp-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 16px;
    line-height: 1.4;
    transition: all 0.2s ease;
    background: white;
}

.brxe-smart-auth .smart-auth-phone-input:focus,
.brxe-smart-auth .smart-auth-otp-input:focus,
[data-smart-auth] .smart-auth-phone-input:focus,
[data-smart-auth] .smart-auth-otp-input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

.brxe-smart-auth .smart-auth-phone-input.error,
.brxe-smart-auth .smart-auth-otp-input.error,
[data-smart-auth] .smart-auth-phone-input.error,
[data-smart-auth] .smart-auth-otp-input.error {
    border-color: #d63638;
    box-shadow: 0 0 0 3px rgba(214, 54, 56, 0.1);
}

/* ==========================================================================
   Error and Success Messages
   ========================================================================== */

.brxe-smart-auth .smart-auth-error-message,
.brxe-smart-auth .smart-auth-success-message,
[data-smart-auth] .smart-auth-error-message,
[data-smart-auth] .smart-auth-success-message {
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
}

.brxe-smart-auth .smart-auth-error-message,
[data-smart-auth] .smart-auth-error-message {
    background: #ffebee;
    color: #d63638;
    border: 1px solid #f5c6cb;
}

.brxe-smart-auth .smart-auth-success-message,
[data-smart-auth] .smart-auth-success-message {
    background: #e6f7e6;
    color: #00a32a;
    border: 1px solid #c3e6cb;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.brxe-smart-auth .smart-auth-loading,
[data-smart-auth] .smart-auth-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.brxe-smart-auth .smart-auth-loading::after,
[data-smart-auth] .smart-auth-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: smart-auth-spin 1s linear infinite;
}

@keyframes smart-auth-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design for Bricks
   ========================================================================== */

@media screen and (max-width: 768px) {
    .brxe-smart-auth.smart-auth-social-login-wrapper.layout-horizontal,
    [data-smart-auth="social-login"].layout-horizontal {
        flex-direction: column;
    }
    
    .brxe-smart-auth .smart-auth-button,
    [data-smart-auth] .smart-auth-button {
        width: 100%;
        justify-content: center;
    }
    
    .brxe-smart-auth.smart-auth-phone-otp-wrapper,
    [data-smart-auth="phone-otp"] {
        max-width: 100%;
    }
}

/* ==========================================================================
   Bricks Builder Editor Compatibility
   ========================================================================== */

.bricks-builder .brxe-smart-auth,
.bricks-builder [data-smart-auth] {
    min-height: 50px;
    border: 2px dashed #ccc;
    position: relative;
}

.bricks-builder .brxe-smart-auth::before,
.bricks-builder [data-smart-auth]::before {
    content: 'Smart Auth Element';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
}

/* Hide placeholder in frontend */
body:not(.bricks-builder) .brxe-smart-auth::before,
body:not(.bricks-builder) [data-smart-auth]::before {
    display: none;
}

body:not(.bricks-builder) .brxe-smart-auth,
body:not(.bricks-builder) [data-smart-auth] {
    border: none;
    min-height: auto;
}
