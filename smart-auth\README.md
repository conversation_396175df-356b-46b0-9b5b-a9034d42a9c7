# Smart Auth WordPress Plugin

A comprehensive WordPress plugin that provides complete authentication and user registration using Firebase Authentication with optional Twilio OTP integration. The plugin is API-first to support future mobile app integrations and follows WordPress coding standards.

## Features

### Core Authentication
- **Firebase Authentication Integration**
  - Google OAuth 2.0
  - Facebook Login
  - Apple Sign-In
  - Phone Number with OTP (Firebase Auth or Twilio Verify API)
- **JWT Token Management** - WordPress-compatible tokens for API access
- **User Synchronization** - Automatic WordPress user creation and data sync
- **Security Features** - Rate limiting, CSRF protection, input sanitization

### WordPress Integration
- **Shortcodes** - Easy integration with any theme
- **Elementor Widgets** - Custom widgets with full styling controls
- **Bricks Builder Elements** - Native elements for Bricks Builder
- **Admin Interface** - Comprehensive settings with tabbed interface
- **REST API** - Complete API endpoints for mobile app integration

## Installation

1. Upload the plugin files to `/wp-content/plugins/smart-auth/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure Firebase and/or Twilio settings in Settings > Smart Auth
4. Use shortcodes or page builder widgets to add authentication forms

## Configuration

### Firebase Setup
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication and configure providers (Google, Facebook, Apple, Phone)
3. Get your project configuration from Project Settings > General
4. Enter the configuration in WordPress Admin > Settings > Smart Auth > Firebase

### Twilio Setup (Optional)
1. Create a Twilio account at https://www.twilio.com
2. Create a Verify Service in the Twilio Console
3. Get your Account SID, Auth Token, and Verify Service SID
4. Enter the configuration in WordPress Admin > Settings > Smart Auth > Twilio

## Usage

### Shortcodes

#### Complete Authentication Form
```php
[smart_auth_form title="Sign In" show_providers="google,facebook,phone" form_style="default" redirect_url="/dashboard"]
```

#### Individual Provider Button
```php
[smart_auth_button provider="google" text="Sign in with Google" redirect_url="/dashboard"]
```

#### OTP Verification Form
```php
[smart_auth_otp phone_required="true" redirect_url="/dashboard"]
```

### Page Builder Widgets

#### Elementor
- Smart Auth Form Widget
- Social Login Buttons Widget
- Phone OTP Form Widget

#### Bricks Builder
- Smart Auth Form Element
- Social Login Element
- Phone OTP Element

## REST API Endpoints

### Authentication
- `POST /wp-json/smart-auth/v1/firebase-verify` - Verify Firebase ID token
- `POST /wp-json/smart-auth/v1/phone-send-otp` - Send OTP to phone number
- `POST /wp-json/smart-auth/v1/phone-verify-otp` - Verify OTP code
- `POST /wp-json/smart-auth/v1/create-user` - Create WordPress user from Firebase data
- `POST /wp-json/smart-auth/v1/refresh-token` - Refresh JWT token

### User Management
- `GET /wp-json/smart-auth/v1/user-profile` - Get current user profile
- `POST /wp-json/smart-auth/v1/user-profile` - Update user profile
- `POST /wp-json/smart-auth/v1/sync-firebase` - Sync user data from Firebase

## File Structure

```
smart-auth/
├── smart-auth.php                          # Main plugin file
├── composer.json                           # Composer dependencies
├── README.md                               # This file
├── includes/                               # Core plugin classes
│   ├── class-smart-auth.php               # Main plugin class
│   ├── class-firebase-auth.php            # Firebase API handler
│   ├── class-twilio-auth.php              # Twilio API handler
│   ├── class-jwt-handler.php              # JWT token management
│   ├── class-user-sync.php                # WordPress user synchronization
│   └── rest-api/                          # REST API endpoints
│       ├── class-auth-endpoints.php       # Authentication endpoints
│       └── class-user-endpoints.php       # User management endpoints
├── admin/                                  # Admin interface
│   ├── class-admin-settings.php           # Settings page handler
│   ├── views/                             # Admin page templates
│   └── assets/                            # Admin CSS/JS
├── widgets/                               # Page builder integrations
│   ├── elementor/                         # Elementor widgets
│   │   ├── class-elementor-widgets.php    # Widget registration
│   │   └── widgets/                       # Individual widget classes
│   └── bricks/                            # Bricks Builder elements
│       ├── class-bricks-elements.php      # Element registration
│       └── elements/                      # Individual element classes
├── assets/                                # Frontend assets
│   ├── js/                               # JavaScript files
│   │   ├── smart-auth.js                 # Main frontend script
│   │   ├── firebase-config.js            # Firebase configuration
│   │   └── otp-handler.js                # OTP handling
│   ├── css/                              # CSS files
│   │   ├── smart-auth.css                # Main stylesheet
│   │   └── admin.css                     # Admin styles
│   └── images/                           # Images and icons
├── templates/                             # Template files
│   ├── auth-form.php                     # Authentication form template
│   ├── otp-form.php                      # OTP form template
│   └── success-message.php               # Success message template
├── languages/                            # Translation files
│   └── smart-auth.pot                    # Translation template
└── vendor/                               # Composer dependencies
```

## Development Status

This plugin is currently in development. The following components have been implemented:

### ✅ Completed
- [x] Plugin structure and main files
- [x] Composer configuration with dependencies
- [x] Main plugin class with hooks and initialization
- [x] Admin settings interface foundation
- [x] Elementor widget structure
- [x] Bricks Builder element structure
- [x] REST API endpoint structure
- [x] Frontend JavaScript and CSS
- [x] Authentication form template
- [x] Translation template

### 🚧 In Progress
- [ ] Firebase Authentication implementation
- [ ] Twilio OTP integration
- [ ] JWT token management
- [ ] User synchronization system
- [ ] Complete admin settings interface
- [ ] Shortcode implementations
- [ ] Security and rate limiting
- [ ] Testing and documentation

## Requirements

- WordPress 5.0 or higher
- PHP 8.0 or higher
- Firebase project (for social authentication)
- Twilio account (optional, for OTP via Twilio)

## Dependencies

- `giggsey/libphonenumber-for-php` - Phone number validation and formatting
- `firebase/jwt` - JWT token handling
- `guzzlehttp/guzzle` - HTTP client for API requests

## License

GPL v2 or later

## Author

**Flavio**  
Website: https://moflavio.xyz  
Email: <EMAIL>

## Support

For support and documentation, visit: https://moflavio.xyz/smart-auth
