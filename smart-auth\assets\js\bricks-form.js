/**
 * Smart Auth Bricks Builder Integration
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Bricks Builder Integration object
     */
    window.SmartAuthBricks = {

        /**
         * Debug mode flag
         */
        debug: false,

        /**
         * Initialize Bricks integration
         */
        init: function() {
            this.log('Initializing Smart Auth Bricks integration');
            this.bindEvents();
            this.initializeForms();
            this.checkDependencies();
        },

        /**
         * Log debug messages
         */
        log: function(message, data) {
            if (this.debug && console && console.log) {
                console.log('[SmartAuthBricks]', message, data || '');
            }
        },

        /**
         * Check for required dependencies
         */
        checkDependencies: function() {
            var missing = [];

            if (typeof window.SmartAuth === 'undefined') {
                missing.push('SmartAuth');
            }

            if (typeof window.SmartAuthOTP === 'undefined') {
                missing.push('SmartAuthOTP');
            }

            if (missing.length > 0) {
                this.log('Missing dependencies:', missing);
                console.warn('[SmartAuthBricks] Missing dependencies:', missing.join(', '));
            }

            return missing.length === 0;
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            var self = this;

            // Wait for DOM to be ready and Bricks to be loaded
            $(document).ready(function() {
                self.initializeBricksElements();
            });

            // Re-initialize when Bricks updates the DOM (for builder mode)
            if (window.bricks && window.bricks.isFrontend) {
                $(document).on('bricks/ajax/load', function() {
                    self.log('Bricks AJAX load detected, re-initializing');
                    self.initializeBricksElements();
                });
            }

            // Social login buttons in Bricks elements
            $(document).on('click', '.smart-auth-social-login-wrapper .smart-auth-button', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.handleSocialLogin($(this));
            });

            // Phone OTP forms in Bricks elements
            $(document).on('click', '.smart-auth-phone-otp-wrapper .smart-auth-send-otp', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.handleSendOTP($(this));
            });

            $(document).on('click', '.smart-auth-phone-otp-wrapper .smart-auth-verify-otp', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.handleVerifyOTP($(this));
            });

            // Form submissions
            $(document).on('submit', '.smart-auth-bricks-form', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.handleFormSubmit($(this));
            });

            // Handle input changes for real-time validation
            $(document).on('input', '.smart-auth-phone-input', function() {
                self.validatePhoneInput($(this));
            });

            $(document).on('input', '.smart-auth-otp-input', function() {
                self.validateOTPInput($(this));
            });
        },

        /**
         * Initialize Bricks-specific elements
         */
        initializeBricksElements: function() {
            var self = this;

            // Find and initialize all Smart Auth elements within Bricks
            $('.brxe-smart-auth, [data-smart-auth]').each(function() {
                var $element = $(this);
                if (!$element.data('smart-auth-initialized')) {
                    self.log('Initializing Bricks element', $element);
                    self.initializeBricksElement($element);
                    $element.data('smart-auth-initialized', true);
                }
            });
        },

        /**
         * Initialize individual Bricks element
         */
        initializeBricksElement: function($element) {
            var elementType = $element.data('smart-auth') || $element.data('element-type');

            switch (elementType) {
                case 'social-login':
                    this.initializeSocialLoginWrapper($element);
                    break;
                case 'phone-otp':
                    this.initializePhoneOTPWrapper($element);
                    break;
                case 'auth-form':
                    this.initializeAuthForm($element);
                    break;
                default:
                    // Try to detect based on classes
                    if ($element.hasClass('smart-auth-social-login-wrapper')) {
                        this.initializeSocialLoginWrapper($element);
                    } else if ($element.hasClass('smart-auth-phone-otp-wrapper')) {
                        this.initializePhoneOTPWrapper($element);
                    }
                    break;
            }
        },
        
        /**
         * Initialize forms on page load
         */
        initializeForms: function() {
            var self = this;
            
            // Initialize social login wrappers
            $('.smart-auth-social-login-wrapper').each(function() {
                self.initializeSocialLoginWrapper($(this));
            });
            
            // Initialize phone OTP wrappers
            $('.smart-auth-phone-otp-wrapper').each(function() {
                self.initializePhoneOTPWrapper($(this));
            });
        },
        
        /**
         * Initialize social login wrapper
         */
        initializeSocialLoginWrapper: function($wrapper) {
            var layout = this.getLayoutClass($wrapper);
            var alignment = this.getAlignmentClass($wrapper);
            var buttonWidth = this.getButtonWidthClass($wrapper);
            
            // Apply layout classes
            $wrapper.addClass('layout-' + layout);
            $wrapper.addClass('align-' + alignment);
            $wrapper.addClass('width-' + buttonWidth);
            
            // Handle custom button width
            if (buttonWidth === 'custom') {
                var customWidth = $wrapper.css('--custom-button-width');
                if (customWidth) {
                    $wrapper.find('.smart-auth-button').css('width', customWidth);
                }
            }
            
            // Add loading capability
            if ($wrapper.hasClass('enable-loading')) {
                this.enableLoadingStates($wrapper);
            }
        },
        
        /**
         * Initialize phone OTP wrapper
         */
        initializePhoneOTPWrapper: function($wrapper) {
            var provider = $wrapper.data('provider') || 'firebase';
            var defaultCountry = $wrapper.data('default-country') || 'US';
            var otpLength = $wrapper.data('otp-length') || 6;
            
            // Set up country selector if enabled
            if ($wrapper.data('show-country-selector')) {
                this.setupCountrySelector($wrapper, defaultCountry);
            }
            
            // Set up phone validation if enabled
            if ($wrapper.data('phone-validation')) {
                this.setupPhoneValidation($wrapper);
            }
            
            // Set up OTP input formatting
            this.setupOTPInput($wrapper, otpLength);
            
            // Set up resend functionality if enabled
            if ($wrapper.data('enable-resend')) {
                this.setupResendFunctionality($wrapper);
            }
        },
        
        /**
         * Handle social login button click
         */
        handleSocialLogin: function($button) {
            var provider = $button.data('provider');
            var $wrapper = $button.closest('.smart-auth-social-login-wrapper');
            var redirectUrl = $wrapper.data('redirect-url');
            
            if (!provider) {
                this.showError($wrapper, 'Invalid provider specified.');
                return;
            }
            
            // Show loading state
            this.setButtonLoading($button);
            
            // Use existing SmartAuth functionality
            if (window.SmartAuth && window.SmartAuth.authenticateWithProvider) {
                window.SmartAuth.authenticateWithProvider(provider, redirectUrl)
                    .then(function(result) {
                        if (result.success) {
                            window.location.href = result.redirect_url || redirectUrl || '/';
                        } else {
                            this.showError($wrapper, result.message);
                        }
                    }.bind(this))
                    .catch(function(error) {
                        this.showError($wrapper, error.message || 'Authentication failed.');
                    }.bind(this))
                    .finally(function() {
                        this.resetButtonLoading($button);
                    }.bind(this));
            } else {
                this.showError($wrapper, 'Smart Auth is not properly initialized.');
                this.resetButtonLoading($button);
            }
        },
        
        /**
         * Handle send OTP
         */
        handleSendOTP: function($button) {
            var $wrapper = $button.closest('.smart-auth-phone-otp-wrapper');
            var $phoneInput = $wrapper.find('.smart-auth-phone-input');
            var phoneNumber = $phoneInput.val().trim();
            var provider = $wrapper.data('provider') || 'firebase';
            
            if (!phoneNumber) {
                this.showError($wrapper, 'Please enter your phone number.');
                return;
            }
            
            // Validate phone number if enabled
            if ($wrapper.data('phone-validation') && !this.isValidPhoneNumber(phoneNumber)) {
                this.showError($wrapper, 'Please enter a valid phone number.');
                return;
            }
            
            // Show loading state
            this.setButtonLoading($button, 'Sending...');
            
            // Use existing SmartAuthOTP functionality
            if (window.SmartAuthOTP) {
                window.SmartAuthOTP.sendOTP($button);
            } else {
                this.showError($wrapper, 'OTP functionality is not available.');
                this.resetButtonLoading($button, 'Send Code');
            }
        },
        
        /**
         * Handle verify OTP
         */
        handleVerifyOTP: function($button) {
            var $wrapper = $button.closest('.smart-auth-phone-otp-wrapper');
            
            // Use existing SmartAuthOTP functionality
            if (window.SmartAuthOTP) {
                window.SmartAuthOTP.verifyOTP($button);
            } else {
                this.showError($wrapper, 'OTP functionality is not available.');
            }
        },
        
        /**
         * Handle form submit
         */
        handleFormSubmit: function($form) {
            var formType = $form.data('form-type');
            
            switch (formType) {
                case 'social-login':
                    this.handleSocialLoginForm($form);
                    break;
                case 'phone-otp':
                    this.handlePhoneOTPForm($form);
                    break;
                default:
                    console.warn('Unknown form type:', formType);
            }
        },
        
        /**
         * Get layout class from wrapper
         */
        getLayoutClass: function($wrapper) {
            var classes = $wrapper.attr('class').split(' ');
            for (var i = 0; i < classes.length; i++) {
                if (classes[i].startsWith('layout-')) {
                    return classes[i].replace('layout-', '');
                }
            }
            return 'vertical';
        },
        
        /**
         * Get alignment class from wrapper
         */
        getAlignmentClass: function($wrapper) {
            var classes = $wrapper.attr('class').split(' ');
            for (var i = 0; i < classes.length; i++) {
                if (classes[i].startsWith('align-')) {
                    return classes[i].replace('align-', '');
                }
            }
            return 'center';
        },
        
        /**
         * Get button width class from wrapper
         */
        getButtonWidthClass: function($wrapper) {
            var classes = $wrapper.attr('class').split(' ');
            for (var i = 0; i < classes.length; i++) {
                if (classes[i].startsWith('width-')) {
                    return classes[i].replace('width-', '');
                }
            }
            return 'full';
        },
        
        /**
         * Enable loading states for buttons
         */
        enableLoadingStates: function($wrapper) {
            $wrapper.find('.smart-auth-button').each(function() {
                var $button = $(this);
                $button.data('original-text', $button.text());
            });
        },
        
        /**
         * Set up country selector
         */
        setupCountrySelector: function($wrapper, defaultCountry) {
            var $selector = $wrapper.find('.smart-auth-country-selector');
            if ($selector.length) {
                $selector.val(defaultCountry);
            }
        },
        
        /**
         * Set up phone validation
         */
        setupPhoneValidation: function($wrapper) {
            var $phoneInput = $wrapper.find('.smart-auth-phone-input');
            
            $phoneInput.on('input', function() {
                var value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
            });
        },
        
        /**
         * Set up OTP input
         */
        setupOTPInput: function($wrapper, otpLength) {
            var $otpInput = $wrapper.find('.smart-auth-otp-input');
            
            $otpInput.attr('maxlength', otpLength);
            
            $otpInput.on('input', function() {
                var value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
                
                // Auto-submit if enabled and complete
                if ($wrapper.data('auto-submit') && value.length === otpLength) {
                    setTimeout(function() {
                        $wrapper.find('.smart-auth-verify-otp').click();
                    }, 500);
                }
            });
        },
        
        /**
         * Set up resend functionality
         */
        setupResendFunctionality: function($wrapper) {
            var cooldown = $wrapper.data('resend-cooldown') || 60;
            var $resendButton = $wrapper.find('.smart-auth-resend-button');
            
            if ($resendButton.length) {
                this.startResendTimer($resendButton, cooldown);
            }
        },
        
        /**
         * Start resend timer
         */
        startResendTimer: function($button, cooldown) {
            var originalText = $button.text();
            var countdown = cooldown;
            
            $button.prop('disabled', true);
            
            var timer = setInterval(function() {
                $button.text('Resend in ' + countdown + 's');
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    $button.prop('disabled', false).text(originalText);
                }
            }, 1000);
        },
        
        /**
         * Validate phone number
         */
        isValidPhoneNumber: function(phoneNumber) {
            // Remove all non-digit characters except +
            var cleaned = phoneNumber.replace(/[^\d+]/g, '');

            // Check for international format or local format
            if (cleaned.startsWith('+')) {
                return cleaned.length >= 11 && cleaned.length <= 15;
            } else {
                return cleaned.length >= 10 && cleaned.length <= 14;
            }
        },

        /**
         * Validate phone input in real-time
         */
        validatePhoneInput: function($input) {
            var phoneNumber = $input.val().trim();
            var $wrapper = $input.closest('.smart-auth-phone-otp-wrapper');
            var $errorContainer = $wrapper.find('.smart-auth-error-message');

            // Clear previous errors
            $input.removeClass('error');
            $errorContainer.hide();

            if (phoneNumber && !this.isValidPhoneNumber(phoneNumber)) {
                $input.addClass('error');
                this.showError($wrapper, 'Please enter a valid phone number');
                return false;
            }

            return true;
        },

        /**
         * Validate OTP input in real-time
         */
        validateOTPInput: function($input) {
            var otpValue = $input.val().trim();
            var $wrapper = $input.closest('.smart-auth-phone-otp-wrapper');
            var expectedLength = $wrapper.data('otp-length') || 6;

            // Auto-submit when OTP is complete
            if (otpValue.length === expectedLength && /^\d+$/.test(otpValue)) {
                var $verifyButton = $wrapper.find('.smart-auth-verify-otp');
                if ($verifyButton.length && !$verifyButton.prop('disabled')) {
                    setTimeout(function() {
                        $verifyButton.click();
                    }, 500);
                }
            }

            return otpValue.length === expectedLength;
        },

        /**
         * Enhanced error handling
         */
        handleError: function($wrapper, error, context) {
            this.log('Error in ' + (context || 'unknown context'), error);

            var errorMessage = 'An unexpected error occurred.';

            if (typeof error === 'string') {
                errorMessage = error;
            } else if (error && error.message) {
                errorMessage = error.message;
            } else if (error && error.code) {
                errorMessage = this.getFirebaseErrorMessage(error.code);
            }

            this.showError($wrapper, errorMessage);
        },

        /**
         * Get user-friendly Firebase error messages
         */
        getFirebaseErrorMessage: function(errorCode) {
            var messages = {
                'auth/invalid-phone-number': 'The phone number is not valid.',
                'auth/missing-phone-number': 'Please enter a phone number.',
                'auth/quota-exceeded': 'SMS quota exceeded. Please try again later.',
                'auth/user-disabled': 'This account has been disabled.',
                'auth/operation-not-allowed': 'Phone authentication is not enabled.',
                'auth/too-many-requests': 'Too many requests. Please try again later.',
                'auth/invalid-verification-code': 'The verification code is incorrect.',
                'auth/invalid-verification-id': 'The verification session has expired.',
                'auth/code-expired': 'The verification code has expired.',
                'auth/popup-blocked': 'Popup was blocked. Please allow popups and try again.',
                'auth/popup-closed-by-user': 'Authentication was cancelled.',
                'auth/cancelled-popup-request': 'Authentication was cancelled.',
                'auth/network-request-failed': 'Network error. Please check your connection.'
            };

            return messages[errorCode] || 'Authentication failed. Please try again.';
        },
        
        /**
         * Show error message
         */
        showError: function($wrapper, message) {
            this.showMessage($wrapper, message, 'error');
        },
        
        /**
         * Show success message
         */
        showSuccess: function($wrapper, message) {
            this.showMessage($wrapper, message, 'success');
        },
        
        /**
         * Show message
         */
        showMessage: function($wrapper, message, type) {
            var $messageContainer = $wrapper.find('.smart-auth-message');
            
            if (!$messageContainer.length) {
                $messageContainer = $('<div class="smart-auth-message"></div>');
                $wrapper.prepend($messageContainer);
            }
            
            $messageContainer
                .removeClass('smart-auth-error smart-auth-success')
                .addClass('smart-auth-' + type)
                .html('<p>' + message + '</p>')
                .show();
        },
        
        /**
         * Set button loading state
         */
        setButtonLoading: function($button, text) {
            var loadingText = text || 'Loading...';
            $button.data('original-text', $button.text());
            $button.prop('disabled', true).text(loadingText).addClass('loading');
        },
        
        /**
         * Reset button loading state
         */
        resetButtonLoading: function($button, text) {
            var originalText = text || $button.data('original-text') || 'Submit';
            $button.prop('disabled', false).text(originalText).removeClass('loading');
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuthBricks.init();
    });
    
})(jQuery);
