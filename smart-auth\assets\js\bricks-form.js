/**
 * Smart Auth Bricks Builder Integration
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Bricks Builder Integration object
     */
    window.SmartAuthBricks = {
        
        /**
         * Initialize Bricks integration
         */
        init: function() {
            this.bindEvents();
            this.initializeForms();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            var self = this;
            
            // Social login buttons in Bricks elements
            $(document).on('click', '.smart-auth-social-login-wrapper .smart-auth-button', function(e) {
                e.preventDefault();
                self.handleSocialLogin($(this));
            });
            
            // Phone OTP forms in Bricks elements
            $(document).on('click', '.smart-auth-phone-otp-wrapper .smart-auth-send-otp', function(e) {
                e.preventDefault();
                self.handleSendOTP($(this));
            });
            
            $(document).on('click', '.smart-auth-phone-otp-wrapper .smart-auth-verify-otp', function(e) {
                e.preventDefault();
                self.handleVerifyOTP($(this));
            });
            
            // Form submissions
            $(document).on('submit', '.smart-auth-bricks-form', function(e) {
                e.preventDefault();
                self.handleFormSubmit($(this));
            });
        },
        
        /**
         * Initialize forms on page load
         */
        initializeForms: function() {
            var self = this;
            
            // Initialize social login wrappers
            $('.smart-auth-social-login-wrapper').each(function() {
                self.initializeSocialLoginWrapper($(this));
            });
            
            // Initialize phone OTP wrappers
            $('.smart-auth-phone-otp-wrapper').each(function() {
                self.initializePhoneOTPWrapper($(this));
            });
        },
        
        /**
         * Initialize social login wrapper
         */
        initializeSocialLoginWrapper: function($wrapper) {
            var layout = this.getLayoutClass($wrapper);
            var alignment = this.getAlignmentClass($wrapper);
            var buttonWidth = this.getButtonWidthClass($wrapper);
            
            // Apply layout classes
            $wrapper.addClass('layout-' + layout);
            $wrapper.addClass('align-' + alignment);
            $wrapper.addClass('width-' + buttonWidth);
            
            // Handle custom button width
            if (buttonWidth === 'custom') {
                var customWidth = $wrapper.css('--custom-button-width');
                if (customWidth) {
                    $wrapper.find('.smart-auth-button').css('width', customWidth);
                }
            }
            
            // Add loading capability
            if ($wrapper.hasClass('enable-loading')) {
                this.enableLoadingStates($wrapper);
            }
        },
        
        /**
         * Initialize phone OTP wrapper
         */
        initializePhoneOTPWrapper: function($wrapper) {
            var provider = $wrapper.data('provider') || 'firebase';
            var defaultCountry = $wrapper.data('default-country') || 'US';
            var otpLength = $wrapper.data('otp-length') || 6;
            
            // Set up country selector if enabled
            if ($wrapper.data('show-country-selector')) {
                this.setupCountrySelector($wrapper, defaultCountry);
            }
            
            // Set up phone validation if enabled
            if ($wrapper.data('phone-validation')) {
                this.setupPhoneValidation($wrapper);
            }
            
            // Set up OTP input formatting
            this.setupOTPInput($wrapper, otpLength);
            
            // Set up resend functionality if enabled
            if ($wrapper.data('enable-resend')) {
                this.setupResendFunctionality($wrapper);
            }
        },
        
        /**
         * Handle social login button click
         */
        handleSocialLogin: function($button) {
            var provider = $button.data('provider');
            var $wrapper = $button.closest('.smart-auth-social-login-wrapper');
            var redirectUrl = $wrapper.data('redirect-url');
            
            if (!provider) {
                this.showError($wrapper, 'Invalid provider specified.');
                return;
            }
            
            // Show loading state
            this.setButtonLoading($button);
            
            // Use existing SmartAuth functionality
            if (window.SmartAuth && window.SmartAuth.authenticateWithProvider) {
                window.SmartAuth.authenticateWithProvider(provider, redirectUrl)
                    .then(function(result) {
                        if (result.success) {
                            window.location.href = result.redirect_url || redirectUrl || '/';
                        } else {
                            this.showError($wrapper, result.message);
                        }
                    }.bind(this))
                    .catch(function(error) {
                        this.showError($wrapper, error.message || 'Authentication failed.');
                    }.bind(this))
                    .finally(function() {
                        this.resetButtonLoading($button);
                    }.bind(this));
            } else {
                this.showError($wrapper, 'Smart Auth is not properly initialized.');
                this.resetButtonLoading($button);
            }
        },
        
        /**
         * Handle send OTP
         */
        handleSendOTP: function($button) {
            var $wrapper = $button.closest('.smart-auth-phone-otp-wrapper');
            var $phoneInput = $wrapper.find('.smart-auth-phone-input');
            var phoneNumber = $phoneInput.val().trim();
            var provider = $wrapper.data('provider') || 'firebase';
            
            if (!phoneNumber) {
                this.showError($wrapper, 'Please enter your phone number.');
                return;
            }
            
            // Validate phone number if enabled
            if ($wrapper.data('phone-validation') && !this.isValidPhoneNumber(phoneNumber)) {
                this.showError($wrapper, 'Please enter a valid phone number.');
                return;
            }
            
            // Show loading state
            this.setButtonLoading($button, 'Sending...');
            
            // Use existing SmartAuthOTP functionality
            if (window.SmartAuthOTP) {
                window.SmartAuthOTP.sendOTP($button);
            } else {
                this.showError($wrapper, 'OTP functionality is not available.');
                this.resetButtonLoading($button, 'Send Code');
            }
        },
        
        /**
         * Handle verify OTP
         */
        handleVerifyOTP: function($button) {
            var $wrapper = $button.closest('.smart-auth-phone-otp-wrapper');
            
            // Use existing SmartAuthOTP functionality
            if (window.SmartAuthOTP) {
                window.SmartAuthOTP.verifyOTP($button);
            } else {
                this.showError($wrapper, 'OTP functionality is not available.');
            }
        },
        
        /**
         * Handle form submit
         */
        handleFormSubmit: function($form) {
            var formType = $form.data('form-type');
            
            switch (formType) {
                case 'social-login':
                    this.handleSocialLoginForm($form);
                    break;
                case 'phone-otp':
                    this.handlePhoneOTPForm($form);
                    break;
                default:
                    console.warn('Unknown form type:', formType);
            }
        },
        
        /**
         * Get layout class from wrapper
         */
        getLayoutClass: function($wrapper) {
            var classes = $wrapper.attr('class').split(' ');
            for (var i = 0; i < classes.length; i++) {
                if (classes[i].startsWith('layout-')) {
                    return classes[i].replace('layout-', '');
                }
            }
            return 'vertical';
        },
        
        /**
         * Get alignment class from wrapper
         */
        getAlignmentClass: function($wrapper) {
            var classes = $wrapper.attr('class').split(' ');
            for (var i = 0; i < classes.length; i++) {
                if (classes[i].startsWith('align-')) {
                    return classes[i].replace('align-', '');
                }
            }
            return 'center';
        },
        
        /**
         * Get button width class from wrapper
         */
        getButtonWidthClass: function($wrapper) {
            var classes = $wrapper.attr('class').split(' ');
            for (var i = 0; i < classes.length; i++) {
                if (classes[i].startsWith('width-')) {
                    return classes[i].replace('width-', '');
                }
            }
            return 'full';
        },
        
        /**
         * Enable loading states for buttons
         */
        enableLoadingStates: function($wrapper) {
            $wrapper.find('.smart-auth-button').each(function() {
                var $button = $(this);
                $button.data('original-text', $button.text());
            });
        },
        
        /**
         * Set up country selector
         */
        setupCountrySelector: function($wrapper, defaultCountry) {
            var $selector = $wrapper.find('.smart-auth-country-selector');
            if ($selector.length) {
                $selector.val(defaultCountry);
            }
        },
        
        /**
         * Set up phone validation
         */
        setupPhoneValidation: function($wrapper) {
            var $phoneInput = $wrapper.find('.smart-auth-phone-input');
            
            $phoneInput.on('input', function() {
                var value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
            });
        },
        
        /**
         * Set up OTP input
         */
        setupOTPInput: function($wrapper, otpLength) {
            var $otpInput = $wrapper.find('.smart-auth-otp-input');
            
            $otpInput.attr('maxlength', otpLength);
            
            $otpInput.on('input', function() {
                var value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
                
                // Auto-submit if enabled and complete
                if ($wrapper.data('auto-submit') && value.length === otpLength) {
                    setTimeout(function() {
                        $wrapper.find('.smart-auth-verify-otp').click();
                    }, 500);
                }
            });
        },
        
        /**
         * Set up resend functionality
         */
        setupResendFunctionality: function($wrapper) {
            var cooldown = $wrapper.data('resend-cooldown') || 60;
            var $resendButton = $wrapper.find('.smart-auth-resend-button');
            
            if ($resendButton.length) {
                this.startResendTimer($resendButton, cooldown);
            }
        },
        
        /**
         * Start resend timer
         */
        startResendTimer: function($button, cooldown) {
            var originalText = $button.text();
            var countdown = cooldown;
            
            $button.prop('disabled', true);
            
            var timer = setInterval(function() {
                $button.text('Resend in ' + countdown + 's');
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    $button.prop('disabled', false).text(originalText);
                }
            }, 1000);
        },
        
        /**
         * Validate phone number
         */
        isValidPhoneNumber: function(phoneNumber) {
            return /^\+?[\d\s\-\(\)]{10,}$/.test(phoneNumber);
        },
        
        /**
         * Show error message
         */
        showError: function($wrapper, message) {
            this.showMessage($wrapper, message, 'error');
        },
        
        /**
         * Show success message
         */
        showSuccess: function($wrapper, message) {
            this.showMessage($wrapper, message, 'success');
        },
        
        /**
         * Show message
         */
        showMessage: function($wrapper, message, type) {
            var $messageContainer = $wrapper.find('.smart-auth-message');
            
            if (!$messageContainer.length) {
                $messageContainer = $('<div class="smart-auth-message"></div>');
                $wrapper.prepend($messageContainer);
            }
            
            $messageContainer
                .removeClass('smart-auth-error smart-auth-success')
                .addClass('smart-auth-' + type)
                .html('<p>' + message + '</p>')
                .show();
        },
        
        /**
         * Set button loading state
         */
        setButtonLoading: function($button, text) {
            var loadingText = text || 'Loading...';
            $button.data('original-text', $button.text());
            $button.prop('disabled', true).text(loadingText).addClass('loading');
        },
        
        /**
         * Reset button loading state
         */
        resetButtonLoading: function($button, text) {
            var originalText = text || $button.data('original-text') || 'Submit';
            $button.prop('disabled', false).text(originalText).removeClass('loading');
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuthBricks.init();
    });
    
})(jQuery);
