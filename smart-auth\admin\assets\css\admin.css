/**
 * Smart Auth Admin Styles
 *
 * @package SmartAuth
 * @since 1.0.0
 */

/* ==========================================================================
   General Admin Styles
   ========================================================================== */

.smart-auth-admin-page {
    max-width: 1200px;
    margin: 20px 0;
}

.smart-auth-admin-page h1 {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 600;
    color: #1d2327;
}

.smart-auth-admin-page h1::before {
    content: "🔐";
    font-size: 32px;
}

.smart-auth-admin-page .nav-tab-wrapper {
    margin-bottom: 0;
    border-bottom: 1px solid #c3c4c7;
    background: #f6f7f7;
    border-radius: 8px 8px 0 0;
    padding: 0 20px;
}

.smart-auth-admin-page .nav-tab {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    margin: 0 4px -1px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    color: #646970;
    text-decoration: none;
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    border-radius: 6px 6px 0 0;
}

.smart-auth-admin-page .nav-tab:hover {
    background: rgba(255, 255, 255, 0.8);
    color: #135e96;
    border-bottom-color: #135e96;
}

.smart-auth-admin-page .nav-tab-active {
    background: #fff;
    color: #135e96;
    border-bottom-color: #135e96;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.smart-auth-admin-page .nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.smart-auth-tab-content {
    background: #fff;
    padding: 30px;
    border: 1px solid #c3c4c7;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.smart-auth-admin-page .form-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.smart-auth-admin-page .form-table th {
    width: 220px;
    padding: 24px 20px;
    vertical-align: top;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
}

.smart-auth-admin-page .form-table td {
    padding: 24px 20px;
    border-bottom: 1px solid #e9ecef;
}

.smart-auth-admin-page .form-table tr:last-child td {
    border-bottom: none;
}

.smart-auth-admin-page .form-table input[type="text"],
.smart-auth-admin-page .form-table input[type="password"],
.smart-auth-admin-page .form-table input[type="number"],
.smart-auth-admin-page .form-table select,
.smart-auth-admin-page .form-table textarea {
    font-size: 14px;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: #fff;
}

.smart-auth-admin-page .form-table input[type="text"]:focus,
.smart-auth-admin-page .form-table input[type="password"]:focus,
.smart-auth-admin-page .form-table input[type="number"]:focus,
.smart-auth-admin-page .form-table select:focus,
.smart-auth-admin-page .form-table textarea:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

.smart-auth-admin-page .form-table .regular-text {
    width: 350px;
}

.smart-auth-admin-page .form-table .large-text {
    width: 500px;
}

.smart-auth-admin-page .form-table .small-text {
    width: 100px;
}

.smart-auth-admin-page .form-table .description {
    margin-top: 12px;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.6;
    font-style: italic;
}

.smart-auth-admin-page .form-table fieldset {
    margin: 0;
    padding: 0;
    border: none;
}

.smart-auth-admin-page .form-table fieldset label {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 400;
    cursor: pointer;
    transition: color 0.2s ease;
}

.smart-auth-admin-page .form-table fieldset label:hover {
    color: #007cba;
}

.smart-auth-admin-page .form-table fieldset label input[type="checkbox"] {
    margin-right: 12px;
    transform: scale(1.2);
}

/* ==========================================================================
   Test Connection Buttons
   ========================================================================== */

.smart-auth-test-connection {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.smart-auth-test-connection .button {
    margin-right: 12px;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.smart-auth-test-connection .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#test-firebase-connection,
#test-twilio-connection {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    border-color: #007cba;
    color: #fff;
}

#test-firebase-connection:hover,
#test-twilio-connection:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%);
}

.smart-auth-test-result {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-left: 12px;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    animation: fadeIn 0.3s ease;
}

.smart-auth-test-result::before {
    font-family: "dashicons";
    font-size: 16px;
}

.smart-auth-test-result.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.smart-auth-test-result.success::before {
    content: "\f147"; /* dashicons-yes */
}

.smart-auth-test-result.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.smart-auth-test-result.error::before {
    content: "\f335"; /* dashicons-no */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ==========================================================================
   JWT Secret Generator
   ========================================================================== */

.smart-auth-jwt-secret-section {
    position: relative;
}

.smart-auth-jwt-secret-section input[type="password"] {
    padding-right: 120px;
}

.smart-auth-jwt-secret-section .button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    height: 28px;
    line-height: 26px;
    padding: 0 12px;
    font-size: 12px;
}

/* ==========================================================================
   Security Settings
   ========================================================================== */

.smart-auth-security-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 10px;
}

.smart-auth-security-card {
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.smart-auth-security-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.smart-auth-security-card .description {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* ==========================================================================
   Status Indicators
   ========================================================================== */

.smart-auth-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.smart-auth-status.configured {
    background: #d1e7dd;
    color: #0f5132;
}

.smart-auth-status.not-configured {
    background: #f8d7da;
    color: #721c24;
}

.smart-auth-status.warning {
    background: #fff3cd;
    color: #856404;
}

/* ==========================================================================
   Copy to Clipboard
   ========================================================================== */

.smart-auth-copy-wrapper {
    position: relative;
    display: inline-block;
}

.smart-auth-copy-wrapper .copy-to-clipboard {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #646970;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.smart-auth-copy-wrapper .copy-to-clipboard:hover {
    background: #f0f0f1;
    color: #135e96;
}

/* ==========================================================================
   Help Text and Documentation
   ========================================================================== */

.smart-auth-help-box {
    background: #f0f6fc;
    border: 1px solid #c9d1d9;
    border-radius: 6px;
    padding: 16px;
    margin: 15px 0;
}

.smart-auth-help-box h4 {
    margin: 0 0 8px 0;
    color: #0969da;
    font-size: 14px;
    font-weight: 600;
}

.smart-auth-help-box p {
    margin: 0 0 8px 0;
    font-size: 13px;
    line-height: 1.5;
}

.smart-auth-help-box p:last-child {
    margin-bottom: 0;
}

.smart-auth-help-box code {
    background: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 12px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.smart-auth-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.smart-auth-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: smart-auth-spin 1s linear infinite;
}

@keyframes smart-auth-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Submit Button Styling
   ========================================================================== */

.smart-auth-admin-page .submit {
    padding: 0;
    margin-top: 30px;
}

.smart-auth-admin-page .submit .button-primary {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    border-color: #007cba;
    color: #fff;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.2);
}

.smart-auth-admin-page .submit .button-primary:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
}

.smart-auth-admin-page .submit .button-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.2);
}

/* ==========================================================================
   Status Indicators Enhancement
   ========================================================================== */

.smart-auth-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    animation: fadeIn 0.3s ease;
}

.smart-auth-status::before {
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: block;
}

.smart-auth-status.configured::before {
    background: #0f5132;
}

.smart-auth-status.not-configured::before {
    background: #721c24;
}

.smart-auth-status.warning::before {
    background: #856404;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.smart-auth-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.smart-auth-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: smart-auth-spin 1s linear infinite;
}

@keyframes smart-auth-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media screen and (max-width: 782px) {
    .smart-auth-admin-page {
        margin: 10px;
    }

    .smart-auth-admin-page h1 {
        font-size: 24px;
        margin-bottom: 20px;
    }

    .smart-auth-admin-page .nav-tab-wrapper {
        padding: 0 10px;
        overflow-x: auto;
        white-space: nowrap;
    }

    .smart-auth-admin-page .nav-tab {
        display: inline-flex;
        margin: 0 2px -1px 0;
        padding: 12px 16px;
        font-size: 13px;
    }

    .smart-auth-tab-content {
        padding: 20px 15px;
    }

    .smart-auth-admin-page .form-table {
        margin: 0;
    }

    .smart-auth-admin-page .form-table th,
    .smart-auth-admin-page .form-table td {
        display: block;
        width: 100%;
        padding: 15px 20px;
        border-right: none;
    }

    .smart-auth-admin-page .form-table th {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        font-size: 14px;
    }

    .smart-auth-admin-page .form-table .regular-text,
    .smart-auth-admin-page .form-table .large-text {
        width: 100%;
        max-width: none;
    }

    .smart-auth-security-grid {
        grid-template-columns: 1fr;
    }

    .smart-auth-test-connection {
        padding: 15px;
    }

    .smart-auth-test-connection .button {
        display: block;
        width: 100%;
        margin: 0 0 10px 0;
        text-align: center;
    }

    .smart-auth-test-result {
        display: block;
        margin: 10px 0 0 0;
        text-align: center;
    }
}

@media screen and (max-width: 480px) {
    .smart-auth-admin-page .nav-tab {
        padding: 10px 12px;
        font-size: 12px;
    }

    .smart-auth-admin-page .nav-tab .dashicons {
        display: none;
    }

    .smart-auth-tab-content {
        padding: 15px 10px;
    }

    .smart-auth-admin-page .form-table th,
    .smart-auth-admin-page .form-table td {
        padding: 12px 15px;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .smart-auth-help-box {
        background: #1c2128;
        border-color: #30363d;
        color: #e6edf3;
    }
    
    .smart-auth-help-box h4 {
        color: #58a6ff;
    }
    
    .smart-auth-help-box code {
        background: #262c36;
        border-color: #30363d;
        color: #e6edf3;
    }
    
    .smart-auth-security-card {
        background: #21262d;
        border-color: #30363d;
        color: #e6edf3;
    }
    
    .smart-auth-security-card h4 {
        color: #e6edf3;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .smart-auth-admin-page .nav-tab-wrapper,
    .smart-auth-admin-page .button,
    .smart-auth-test-connection {
        display: none;
    }
    
    .smart-auth-admin-page .tab-content {
        border: none;
        box-shadow: none;
    }
}
