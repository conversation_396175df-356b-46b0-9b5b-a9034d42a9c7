<?php
/**
 * Bricks Builder Elements Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Bricks Elements class
 */
class Smart_Auth_Bricks_Elements {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_elements'), 11);
        add_filter('bricks/elements/categories', array($this, 'add_element_category'));
    }

    /**
     * Add Smart Auth element category
     */
    public function add_element_category($categories) {
        $categories['smart-auth'] = array(
            'title' => esc_html__('Smart Auth', 'smart-auth'),
            'icon' => 'ti-lock',
        );

        return $categories;
    }

    /**
     * Register elements
     */
    public function register_elements() {
        // Check if <PERSON><PERSON> is active
        if (!class_exists('\Bricks\Elements')) {
            return;
        }

        // Load element files
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-auth-form-element.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-social-login-element.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-phone-otp-element.php';

        // Register elements with class names (not file paths)
        $elements = array(
            'Smart_Auth_Bricks_Auth_Form_Element',
            'Smart_Auth_Bricks_Social_Login_Element',
            'Smart_Auth_Bricks_Phone_OTP_Element',
        );

        foreach ($elements as $element_class) {
            if (class_exists($element_class)) {
                \Bricks\Elements::register_element($element_class);
            }
        }
    }
}
