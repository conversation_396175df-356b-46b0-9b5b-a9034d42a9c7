/**
 * Smart Auth Admin JavaScript
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Smart Auth Admin object
     */
    window.SmartAuthAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initFormValidation();
            this.initAccessibility();
        },

        /**
         * Get nonce value
         */
        getNonce: function() {
            return smartAuthAdmin.nonce || $('#smart_auth_admin_nonce').val() || '';
        },

        /**
         * Initialize form validation
         */
        initFormValidation: function() {
            var self = this;

            // Real-time validation for required fields
            $('.smart-auth-input[required], .smart-auth-input').on('blur', function() {
                self.validateField($(this));
            });

            // Form submission validation
            $('.smart-auth-form').on('submit', function(e) {
                if (!self.validateForm($(this))) {
                    e.preventDefault();
                    return false;
                }
            });
        },

        /**
         * Validate individual field
         */
        validateField: function($field) {
            var value = $field.val().trim();
            var fieldName = $field.attr('name') || $field.attr('id');
            var isValid = true;
            var errorMessage = '';

            // Remove existing validation classes
            $field.removeClass('smart-auth-input-error smart-auth-input-success');
            $field.siblings('.smart-auth-field-error').remove();

            // Check if field is required
            if ($field.prop('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Field-specific validation
            if (value && fieldName) {
                if (fieldName.includes('api_key') && value.length < 20) {
                    isValid = false;
                    errorMessage = 'API key appears to be too short.';
                } else if (fieldName.includes('project_id') && !/^[a-z0-9-]+$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Project ID should only contain lowercase letters, numbers, and hyphens.';
                } else if (fieldName.includes('auth_domain') && !value.includes('.')) {
                    isValid = false;
                    errorMessage = 'Auth domain should be a valid domain.';
                }
            }

            // Apply validation styling
            if (!isValid) {
                $field.addClass('smart-auth-input-error');
                $field.after('<div class="smart-auth-field-error">' + errorMessage + '</div>');
            } else if (value) {
                $field.addClass('smart-auth-input-success');
            }

            return isValid;
        },

        /**
         * Validate entire form
         */
        validateForm: function($form) {
            var self = this;
            var isValid = true;

            $form.find('.smart-auth-input').each(function() {
                if (!self.validateField($(this))) {
                    isValid = false;
                }
            });

            return isValid;
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // Test connection buttons
            $(document).on('click', '#test-firebase-connection', this.testFirebaseConnection);
            $(document).on('click', '#test-twilio-connection', this.testTwilioConnection);
            $(document).on('click', '#generate-jwt-secret', this.generateJWTSecret);
            
            // Form validation
            $(document).on('submit', 'form[action="options.php"]', this.validateForm);
            
            // Dynamic field visibility
            $(document).on('change', 'input[name="smart_auth_security_settings[enable_captcha]"]', this.toggleCaptchaFields);
            $(document).on('change', 'input[name="smart_auth_security_settings[enable_ip_blocking]"]', this.toggleIPBlockingFields);
            $(document).on('change', 'input[name="smart_auth_user_settings[sync_profile_picture]"]', this.toggleProfilePictureFields);
            
            // Copy to clipboard functionality
            $(document).on('click', '.copy-to-clipboard', this.copyToClipboard);
        },
        
        /**
         * Initialize tab functionality
         */
        initTabs: function() {
            var self = this;

            // Handle tab switching with new nav structure
            $('.smart-auth-nav-tab').on('click', function(e) {
                e.preventDefault();

                var $this = $(this);
                var tab = $this.data('tab') || $this.attr('href').split('tab=')[1] || 'firebase';

                self.switchTab(tab);

                // Update URL without page reload
                if (history.pushState) {
                    var newUrl = window.location.protocol + "//" + window.location.host +
                                window.location.pathname + '?page=smart-auth-settings&tab=' + tab;
                    window.history.pushState({path: newUrl}, '', newUrl);
                }
            });

            // Handle browser back/forward buttons
            window.addEventListener('popstate', function(event) {
                var urlParams = new URLSearchParams(window.location.search);
                var tab = urlParams.get('tab') || 'firebase';
                self.switchTab(tab);
            });

            // Initialize active tab on page load
            var urlParams = new URLSearchParams(window.location.search);
            var activeTab = urlParams.get('tab') || 'firebase';
            this.switchTab(activeTab);
        },

        /**
         * Switch to specific tab
         */
        switchTab: function(tab) {
            // Update active tab with new class structure and ARIA attributes
            $('.smart-auth-nav-tab').removeClass('active').attr('aria-selected', 'false');
            var $activeTab = $('.smart-auth-nav-tab[data-tab="' + tab + '"]');
            $activeTab.addClass('active').attr('aria-selected', 'true');

            // Fallback for href-based tabs
            if ($activeTab.length === 0) {
                $activeTab = $('.smart-auth-nav-tab[href*="tab=' + tab + '"]');
                $activeTab.addClass('active').attr('aria-selected', 'true');
            }

            // Show/hide tab content with new panel structure
            $('.smart-auth-tab-panel').hide().attr('aria-hidden', 'true');
            var $activePanel = $('.smart-auth-tab-panel[data-tab="' + tab + '"]');
            $activePanel.show().attr('aria-hidden', 'false');

            // Announce tab change to screen readers
            this.announceTabChange(tab);

            // Fallback for old tab content structure
            if ($activePanel.length === 0) {
                $('.tab-content > form, .tab-content > div').hide();
                $('.tab-content-' + tab + ', .smart-auth-' + tab + '-tab').show();
            }
        },

        /**
         * Announce tab change to screen readers
         */
        announceTabChange: function(tab) {
            var tabNames = {
                'firebase': 'Firebase',
                'twilio': 'Twilio',
                'jwt': 'JWT',
                'user': 'User Settings',
                'security': 'Security'
            };

            var message = 'Switched to ' + (tabNames[tab] || tab) + ' tab';
            this.announceToScreenReader(message);
        },

        /**
         * Announce message to screen readers
         */
        announceToScreenReader: function(message) {
            var $liveRegion = $('#smart-auth-live-region');
            if ($liveRegion.length === 0) {
                $liveRegion = $('<div id="smart-auth-live-region" class="smart-auth-live-region" aria-live="polite" aria-atomic="true"></div>');
                $('body').append($liveRegion);
            }

            $liveRegion.text(message);

            // Clear the message after a short delay
            setTimeout(function() {
                $liveRegion.empty();
            }, 1000);
        },

        /**
         * Initialize accessibility features
         */
        initAccessibility: function() {
            this.handleKeyboardNavigation();
            this.initAriaAttributes();
        },

        /**
         * Handle keyboard navigation
         */
        handleKeyboardNavigation: function() {
            var self = this;

            // Tab navigation with arrow keys
            $('.smart-auth-nav-tab').on('keydown', function(e) {
                var $tabs = $('.smart-auth-nav-tab');
                var currentIndex = $tabs.index(this);
                var $target;

                switch (e.which) {
                    case 37: // Left arrow
                    case 38: // Up arrow
                        e.preventDefault();
                        $target = currentIndex > 0 ? $tabs.eq(currentIndex - 1) : $tabs.last();
                        break;
                    case 39: // Right arrow
                    case 40: // Down arrow
                        e.preventDefault();
                        $target = currentIndex < $tabs.length - 1 ? $tabs.eq(currentIndex + 1) : $tabs.first();
                        break;
                    case 13: // Enter
                    case 32: // Space
                        e.preventDefault();
                        $(this).click();
                        return;
                }

                if ($target) {
                    $target.focus();
                    var tab = $target.data('tab') || $target.attr('href').split('tab=')[1];
                    self.switchTab(tab);
                }
            });
        },

        /**
         * Initialize ARIA attributes
         */
        initAriaAttributes: function() {
            // Set initial ARIA states
            $('.smart-auth-tab-panel').each(function() {
                var $panel = $(this);
                var isVisible = $panel.is(':visible');
                $panel.attr('aria-hidden', !isVisible);
            });

            // Add role attributes if missing
            if (!$('.smart-auth-nav-tabs').attr('role')) {
                $('.smart-auth-nav-tabs').attr('role', 'tablist');
            }

            $('.smart-auth-nav-tab').each(function() {
                var $tab = $(this);
                if (!$tab.attr('role')) {
                    $tab.attr('role', 'tab');
                }
            });

            $('.smart-auth-tab-panel').each(function() {
                var $panel = $(this);
                if (!$panel.attr('role')) {
                    $panel.attr('role', 'tabpanel');
                }
            });
        },

        /**
         * Test Firebase connection
         */
        testFirebaseConnection: function(e) {
            e.preventDefault();

            var $button = $(this);
            var $result = $('#firebase-test-result');
            var originalText = $button.text();

            // Show loading state with new design
            $button.prop('disabled', true).addClass('smart-auth-loading');
            $button.find('.dashicons').removeClass('dashicons-admin-network').addClass('dashicons-update');
            $button.contents().filter(function() {
                return this.nodeType === 3; // Text nodes
            }).last().replaceWith(' ' + (smartAuthAdmin.strings.testing || 'Testing...'));

            $result.removeClass('success error').addClass('loading')
                   .html('<span class="dashicons dashicons-update"></span>' + (smartAuthAdmin.strings.testing || 'Testing...'));

            // Get form data with new field IDs
            var formData = {
                action: 'smart_auth_test_firebase',
                nonce: smartAuthAdmin.nonce || $('#smart_auth_admin_nonce').val(),
                project_id: $('#firebase_project_id').val() || $('input[name="smart_auth_firebase_settings[project_id]"]').val(),
                api_key: $('#firebase_api_key').val() || $('input[name="smart_auth_firebase_settings[api_key]"]').val(),
                auth_domain: $('#firebase_auth_domain').val() || $('input[name="smart_auth_firebase_settings[auth_domain]"]').val(),
                storage_bucket: $('#firebase_storage_bucket').val() || $('input[name="smart_auth_firebase_settings[storage_bucket]"]').val()
            };

            $.ajax({
                url: smartAuthAdmin.ajaxUrl || ajaxurl,
                type: 'POST',
                data: formData,
                timeout: 15000,
                success: function(response) {
                    if (response.success) {
                        $result.removeClass('loading error').addClass('success')
                               .html('<span class="dashicons dashicons-yes-alt"></span>' +
                                    (response.data.message || smartAuthAdmin.strings.connectionSuccess || 'Connection successful!'));

                        // Update nav badge if it exists
                        $('.smart-auth-nav-tab[data-tab="firebase"] .smart-auth-nav-badge')
                            .removeClass('not-configured').addClass('configured').text('Configured');
                    } else {
                        var errorMsg = response.data && response.data.message ?
                                      response.data.message : (smartAuthAdmin.strings.connectionFailed || 'Connection failed');
                        $result.removeClass('loading success').addClass('error')
                               .html('<span class="dashicons dashicons-dismiss"></span>' + errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    var errorMsg = status === 'timeout' ?
                                  'Connection timeout. Please check your settings.' :
                                  (smartAuthAdmin.strings.connectionFailed || 'Connection test failed. Please try again.');
                    $result.removeClass('loading success').addClass('error')
                           .html('<span class="dashicons dashicons-dismiss"></span>' + errorMsg);
                },
                complete: function() {
                    // Restore button state
                    $button.prop('disabled', false).removeClass('smart-auth-loading');
                    $button.find('.dashicons').removeClass('dashicons-update').addClass('dashicons-admin-network');
                    $button.contents().filter(function() {
                        return this.nodeType === 3;
                    }).last().replaceWith(' ' + originalText.replace(/Testing\.\.\.?/, '').trim());

                    // Hide result after 8 seconds
                    setTimeout(function() {
                        $result.removeClass('success error loading').empty();
                    }, 8000);
                }
            });
        },
        
        /**
         * Test Twilio connection
         */
        testTwilioConnection: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $result = $('#twilio-test-result');
            
            // Show loading state
            $button.prop('disabled', true).text('Testing...');
            $result.removeClass('success error').text('');
            
            // Get form data
            var formData = {
                action: 'smart_auth_test_twilio',
                nonce: SmartAuthAdmin.getNonce(),
                account_sid: $('input[name="smart_auth_twilio_settings[account_sid]"]').val(),
                auth_token: $('input[name="smart_auth_twilio_settings[auth_token]"]').val(),
                verify_service_sid: $('input[name="smart_auth_twilio_settings[verify_service_sid]"]').val()
            };
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $result.addClass('success').text(response.data.message);
                    } else {
                        $result.addClass('error').text(response.data.message);
                    }
                },
                error: function() {
                    $result.addClass('error').text('Connection test failed. Please try again.');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test Connection');
                }
            });
        },
        
        /**
         * Generate JWT secret
         */
        generateJWTSecret: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $input = $('input[name="smart_auth_jwt_settings[secret_key]"]');
            
            if (!confirm('Are you sure you want to generate a new JWT secret? This will invalidate all existing tokens.')) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).text('Generating...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'smart_auth_generate_jwt_secret',
                    nonce: SmartAuthAdmin.getNonce()
                },
                success: function(response) {
                    if (response.success) {
                        $input.val(response.data.secret);
                        SmartAuthAdmin.showNotice(response.data.message, 'success');
                    } else {
                        SmartAuthAdmin.showNotice('Failed to generate secret.', 'error');
                    }
                },
                error: function() {
                    SmartAuthAdmin.showNotice('Failed to generate secret.', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Generate New Secret');
                }
            });
        },
        
        /**
         * Validate form before submission
         */
        validateForm: function(e) {
            var $form = $(this);
            var isValid = true;
            var errors = [];
            
            // Firebase validation
            if ($form.find('input[name*="firebase"]').length > 0) {
                var projectId = $form.find('input[name="smart_auth_firebase_settings[project_id]"]').val();
                var apiKey = $form.find('input[name="smart_auth_firebase_settings[api_key]"]').val();
                
                if (projectId && !apiKey) {
                    errors.push('Firebase API Key is required when Project ID is provided.');
                    isValid = false;
                }
                
                if (apiKey && !projectId) {
                    errors.push('Firebase Project ID is required when API Key is provided.');
                    isValid = false;
                }
            }
            
            // Twilio validation
            if ($form.find('input[name*="twilio"]').length > 0) {
                var accountSid = $form.find('input[name="smart_auth_twilio_settings[account_sid]"]').val();
                var authToken = $form.find('input[name="smart_auth_twilio_settings[auth_token]"]').val();
                var serviceSid = $form.find('input[name="smart_auth_twilio_settings[verify_service_sid]"]').val();
                
                if ((accountSid || authToken || serviceSid) && (!accountSid || !authToken || !serviceSid)) {
                    errors.push('All Twilio fields are required when configuring Twilio.');
                    isValid = false;
                }
            }
            
            // Show errors if any
            if (!isValid) {
                e.preventDefault();
                SmartAuthAdmin.showNotice(errors.join('<br>'), 'error');
            }
            
            return isValid;
        },
        
        /**
         * Toggle CAPTCHA fields visibility
         */
        toggleCaptchaFields: function() {
            var $checkbox = $(this);
            var $fields = $checkbox.closest('fieldset').find('label').not(':first');
            
            if ($checkbox.is(':checked')) {
                $fields.show();
            } else {
                $fields.hide();
            }
        },
        
        /**
         * Toggle IP blocking fields visibility
         */
        toggleIPBlockingFields: function() {
            var $checkbox = $(this);
            var $textarea = $checkbox.closest('fieldset').find('textarea').closest('label');
            
            if ($checkbox.is(':checked')) {
                $textarea.show();
            } else {
                $textarea.hide();
            }
        },
        
        /**
         * Toggle profile picture fields visibility
         */
        toggleProfilePictureFields: function() {
            var $checkbox = $(this);
            var $downloadCheckbox = $('input[name="smart_auth_user_settings[download_profile_picture]"]').closest('label');
            
            if ($checkbox.is(':checked')) {
                $downloadCheckbox.show();
            } else {
                $downloadCheckbox.hide();
            }
        },
        
        /**
         * Copy text to clipboard
         */
        copyToClipboard: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var text = $button.data('text') || $button.prev('input').val();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    SmartAuthAdmin.showNotice('Copied to clipboard!', 'success');
                });
            } else {
                // Fallback for older browsers
                var $temp = $('<textarea>');
                $('body').append($temp);
                $temp.val(text).select();
                document.execCommand('copy');
                $temp.remove();
                SmartAuthAdmin.showNotice('Copied to clipboard!', 'success');
            }
        },
        
        /**
         * Show admin notice
         */
        showNotice: function(message, type) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $notice.remove();
                });
            }, 5000);
        },
        
        /**
         * Get admin nonce
         */
        getNonce: function() {
            return $('#smart_auth_admin_nonce').val() || '';
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuthAdmin.init();
        
        // Initialize field visibility on page load
        $('input[name="smart_auth_security_settings[enable_captcha]"]').trigger('change');
        $('input[name="smart_auth_security_settings[enable_ip_blocking]"]').trigger('change');
        $('input[name="smart_auth_user_settings[sync_profile_picture]"]').trigger('change');
    });
    
})(jQuery);
