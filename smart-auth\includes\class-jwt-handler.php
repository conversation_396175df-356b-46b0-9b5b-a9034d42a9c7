<?php
/**
 * JWT Token Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Import Firebase JWT library
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\InvalidTokenException;

/**
 * JWT Handler class
 */
class Smart_Auth_JWT_Handler {

    /**
     * JWT settings
     *
     * @var array
     */
    private $settings;

    /**
     * JWT algorithm
     *
     * @var string
     */
    private $algorithm = 'HS256';

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('smart_auth_jwt_settings', array());

        // Ensure we have a secret key
        if (empty($this->settings['secret_key'])) {
            $this->settings['secret_key'] = $this->generate_secret_key();
            update_option('smart_auth_jwt_settings', $this->settings);
        }
    }

    /**
     * Generate JWT token for user
     *
     * @param WP_User $user WordPress user object
     * @param array $extra_data Additional data to include in token
     * @return string|WP_Error JWT token or error
     */
    public function generate_token($user, $extra_data = array()) {
        if (!class_exists('Firebase\JWT\JWT')) {
            return new WP_Error('jwt_library_missing', __('JWT library is not available.', 'smart-auth'));
        }

        if (!$user || !($user instanceof WP_User)) {
            return new WP_Error('invalid_user', __('Invalid user provided.', 'smart-auth'));
        }

        $secret_key = $this->get_secret_key();
        if (empty($secret_key)) {
            return new WP_Error('missing_secret', __('JWT secret key is not configured.', 'smart-auth'));
        }

        $issued_at = time();
        $expiration = $issued_at + $this->get_expiration_time();

        // Build payload
        $payload = array(
            'iss' => get_site_url(), // Issuer
            'aud' => get_site_url(), // Audience
            'iat' => $issued_at, // Issued at
            'nbf' => $issued_at, // Not before
            'exp' => $expiration, // Expiration
            'data' => array(
                'user' => array(
                    'id' => $user->ID,
                    'user_login' => $user->user_login,
                    'user_email' => $user->user_email,
                    'user_nicename' => $user->user_nicename,
                    'display_name' => $user->display_name,
                ),
            ),
        );

        // Add Firebase UID if available
        $firebase_uid = get_user_meta($user->ID, 'firebase_uid', true);
        if (!empty($firebase_uid)) {
            $payload['data']['firebase_uid'] = $firebase_uid;
        }

        // Add auth provider if available
        $auth_provider = get_user_meta($user->ID, 'auth_provider', true);
        if (!empty($auth_provider)) {
            $payload['data']['auth_provider'] = $auth_provider;
        }

        // Add extra data
        if (!empty($extra_data)) {
            $payload['data'] = array_merge($payload['data'], $extra_data);
        }

        // Apply filter to allow customization
        $payload = apply_filters('smart_auth_jwt_payload', $payload, $user);

        try {
            $token = JWT::encode($payload, $secret_key, $this->algorithm);

            // Store token hash for validation (optional security measure)
            $this->store_token_hash($user->ID, $token);

            return $token;

        } catch (Exception $e) {
            error_log('Smart Auth JWT Generation Error: ' . $e->getMessage());
            return new WP_Error('jwt_generation_error', __('Failed to generate JWT token.', 'smart-auth'));
        }
    }

    /**
     * Validate JWT token
     *
     * @param string $token JWT token
     * @return array|WP_Error Token payload or error
     */
    public function validate_token($token) {
        if (!class_exists('Firebase\JWT\JWT')) {
            return new WP_Error('jwt_library_missing', __('JWT library is not available.', 'smart-auth'));
        }

        if (empty($token)) {
            return new WP_Error('missing_token', __('JWT token is required.', 'smart-auth'));
        }

        $secret_key = $this->get_secret_key();
        if (empty($secret_key)) {
            return new WP_Error('missing_secret', __('JWT secret key is not configured.', 'smart-auth'));
        }

        try {
            $decoded = JWT::decode($token, new Key($secret_key, $this->algorithm));

            // Convert to array
            $payload = json_decode(json_encode($decoded), true);

            // Validate basic claims
            $validation_result = $this->validate_token_claims($payload);
            if (is_wp_error($validation_result)) {
                return $validation_result;
            }

            // Check if user still exists
            if (isset($payload['data']['user']['id'])) {
                $user = get_user_by('id', $payload['data']['user']['id']);
                if (!$user) {
                    return new WP_Error('user_not_found', __('User no longer exists.', 'smart-auth'));
                }
            }

            return $payload;

        } catch (ExpiredException $e) {
            return new WP_Error('token_expired', __('JWT token has expired.', 'smart-auth'));
        } catch (InvalidTokenException $e) {
            return new WP_Error('invalid_token', __('Invalid JWT token.', 'smart-auth'));
        } catch (Exception $e) {
            error_log('Smart Auth JWT Validation Error: ' . $e->getMessage());
            return new WP_Error('jwt_validation_error', __('Failed to validate JWT token.', 'smart-auth'));
        }
    }

    /**
     * Refresh JWT token
     *
     * @param string $token Current JWT token
     * @return string|WP_Error New JWT token or error
     */
    public function refresh_token($token) {
        // First validate the current token
        $payload = $this->validate_token($token);
        if (is_wp_error($payload)) {
            // If token is expired, we can still refresh it if it's within the refresh window
            if ($payload->get_error_code() !== 'token_expired') {
                return $payload;
            }

            // Try to decode without validation to check if it's within refresh window
            $payload = $this->decode_token_without_validation($token);
            if (is_wp_error($payload)) {
                return $payload;
            }
        }

        // Check if token is within refresh window
        $refresh_window = $this->get_refresh_window();
        $current_time = time();
        $token_exp = isset($payload['exp']) ? $payload['exp'] : 0;

        if ($current_time > ($token_exp + $refresh_window)) {
            return new WP_Error('refresh_expired', __('Token refresh window has expired.', 'smart-auth'));
        }

        // Get user and generate new token
        if (!isset($payload['data']['user']['id'])) {
            return new WP_Error('invalid_payload', __('Invalid token payload.', 'smart-auth'));
        }

        $user = get_user_by('id', $payload['data']['user']['id']);
        if (!$user) {
            return new WP_Error('user_not_found', __('User no longer exists.', 'smart-auth'));
        }

        // Preserve extra data from original token
        $extra_data = array();
        if (isset($payload['data']['firebase_uid'])) {
            $extra_data['firebase_uid'] = $payload['data']['firebase_uid'];
        }
        if (isset($payload['data']['auth_provider'])) {
            $extra_data['auth_provider'] = $payload['data']['auth_provider'];
        }

        return $this->generate_token($user, $extra_data);
    }

    /**
     * Validate token claims
     *
     * @param array $payload Token payload
     * @return bool|WP_Error True if valid, error otherwise
     */
    private function validate_token_claims($payload) {
        $site_url = get_site_url();

        // Check issuer
        if (!isset($payload['iss']) || $payload['iss'] !== $site_url) {
            return new WP_Error('invalid_issuer', __('Invalid token issuer.', 'smart-auth'));
        }

        // Check audience
        if (!isset($payload['aud']) || $payload['aud'] !== $site_url) {
            return new WP_Error('invalid_audience', __('Invalid token audience.', 'smart-auth'));
        }

        // Check expiration
        if (!isset($payload['exp']) || $payload['exp'] < time()) {
            return new WP_Error('token_expired', __('Token has expired.', 'smart-auth'));
        }

        // Check not before
        if (isset($payload['nbf']) && $payload['nbf'] > time()) {
            return new WP_Error('token_not_active', __('Token is not yet active.', 'smart-auth'));
        }

        // Check issued at
        if (isset($payload['iat']) && $payload['iat'] > time()) {
            return new WP_Error('token_future', __('Token issued in the future.', 'smart-auth'));
        }

        return true;
    }

    /**
     * Decode token without validation (for refresh purposes)
     *
     * @param string $token JWT token
     * @return array|WP_Error Token payload or error
     */
    private function decode_token_without_validation($token) {
        try {
            // Split token to get payload
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return new WP_Error('invalid_token_format', __('Invalid token format.', 'smart-auth'));
            }

            // JWT uses URL-safe base64 encoding, so we need to handle it properly
            $payload_encoded = $parts[1];
            // Add padding if needed
            $payload_encoded .= str_repeat('=', (4 - strlen($payload_encoded) % 4) % 4);
            // Convert URL-safe base64 to regular base64
            $payload_encoded = str_replace(['-', '_'], ['+', '/'], $payload_encoded);
            $payload = json_decode(base64_decode($payload_encoded), true);
            if (!$payload) {
                return new WP_Error('invalid_payload', __('Invalid token payload.', 'smart-auth'));
            }

            return $payload;

        } catch (Exception $e) {
            return new WP_Error('decode_error', __('Failed to decode token.', 'smart-auth'));
        }
    }

    /**
     * Generate secret key
     *
     * @return string Secret key
     */
    private function generate_secret_key() {
        return wp_generate_password(64, true, true);
    }

    /**
     * Get secret key
     *
     * @return string Secret key
     */
    private function get_secret_key() {
        if (!empty($this->settings['secret_key'])) {
            return $this->settings['secret_key'];
        }

        // Check if JWT Authentication plugin is active and use its key
        if (defined('JWT_AUTH_SECRET_KEY')) {
            return JWT_AUTH_SECRET_KEY;
        }

        // Fallback to WordPress secret keys
        if (defined('AUTH_KEY')) {
            return AUTH_KEY;
        }

        return '';
    }

    /**
     * Get token expiration time
     *
     * @return int Expiration time in seconds
     */
    private function get_expiration_time() {
        $default_expiration = 24 * HOUR_IN_SECONDS; // 24 hours

        if (isset($this->settings['expiration']) && is_numeric($this->settings['expiration'])) {
            return (int) $this->settings['expiration'];
        }

        return $default_expiration;
    }

    /**
     * Get refresh window time
     *
     * @return int Refresh window in seconds
     */
    private function get_refresh_window() {
        $default_window = 7 * DAY_IN_SECONDS; // 7 days

        if (isset($this->settings['refresh_window']) && is_numeric($this->settings['refresh_window'])) {
            return (int) $this->settings['refresh_window'];
        }

        return $default_window;
    }

    /**
     * Store token hash for additional security
     *
     * @param int $user_id User ID
     * @param string $token JWT token
     */
    private function store_token_hash($user_id, $token) {
        $hash = hash('sha256', $token);
        $cache_key = 'smart_auth_token_' . $user_id;

        // Store for the token's lifetime
        set_transient($cache_key, $hash, $this->get_expiration_time());
    }

    /**
     * Extract token from Authorization header
     *
     * @return string|null Token or null if not found
     */
    public function get_token_from_header() {
        $auth_header = null;

        // Check for Authorization header
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        } elseif (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
            $auth_header = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
        } elseif (function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            if (isset($headers['Authorization'])) {
                $auth_header = $headers['Authorization'];
            }
        }

        if (!$auth_header) {
            return null;
        }

        // Extract Bearer token
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Get JWT settings
     *
     * @return array JWT settings
     */
    public function get_settings() {
        return $this->settings;
    }

    /**
     * Update JWT settings
     *
     * @param array $settings New settings
     * @return bool True on success, false on failure
     */
    public function update_settings($settings) {
        $this->settings = $settings;
        return update_option('smart_auth_jwt_settings', $settings);
    }

    /**
     * Check if JWT Authentication plugin is active
     *
     * @return bool True if active, false otherwise
     */
    public function is_jwt_auth_plugin_active() {
        return defined('JWT_AUTH_SECRET_KEY') || class_exists('Jwt_Auth_Public');
    }
}
